import SwiftUI

// 底部导航栏组件
struct BottomNavigationBar: View {
    @Binding var selectedTabIndex: Int
    let geometry: GeometryProxy
    @EnvironmentObject var userState: UserState
    @State private var showToast = false // 添加Toast提示状态
    @State private var toastMessage = "" // Toast提示消息
    
    // 导航栏项的数据结构
    struct NavigationItem {
        let title: String
        let icon: (selected: String, unselected: String) // 使用元组存储选中和未选中的图标名
    }
    
    // 导航栏项的数据
    let navigationItems = [
        NavigationItem(title: "社区", icon: (selected: "5-1", unselected: "5-8")),
        NavigationItem(title: "AI创作", icon: (selected: "5-2", unselected: "5-2")), // AI创作的图标不变化
        NavigationItem(title: "我", icon: (selected: "5-9", unselected: "5-3"))
    ]
    
    var body: some View {
        ZStack {
            // 导航栏背景
            Rectangle()
                .fill(Color.white)
                .padding(.top, 15)
                .frame(height: geometry.size.height * 0.074+geometry.safeAreaInsets.bottom)
                .edgesIgnoringSafeArea(.bottom)
            
            
            // 导航图标
            HStack {
                // 社区按钮
                VStack(spacing: 4) {
                    Image(0 == selectedTabIndex ? navigationItems[0].icon.selected : navigationItems[0].icon.unselected)
                        .resizable()
                        .scaledToFit()
                        .frame(
                            width: geometry.size.width * 0.075,
                            height: geometry.size.height * 0.035
                        )
                    
                    Text(navigationItems[0].title)
                        .font(.system(
                            size: geometry.size.width * 0.032,
                            weight: .semibold
                        ))
                        .foregroundColor(0 == selectedTabIndex ? .black : Color(red: 0.27, green: 0.18, blue: 0.13))
                }
                .frame(maxWidth: .infinity)
                .onTapGesture {
                    selectedTabIndex = 0
                    userState.selectedTab = 0
                }
                
                // AI创作按钮（中间凸起的圆形按钮）
                Spacer()
                ZStack {
                    // 外部阴影效果 - 增强阴影
                    Circle()
                        .fill(Color.clear)
                        .frame(width: 78, height: 78)
                        .shadow(color: Color.black.opacity(0.5), radius: 8, x: 0, y: 4)
                    
                    // 外部光晕效果
                    Circle()
                        .fill(
                            RadialGradient(
                                gradient: Gradient(colors: [Color.yellow.opacity(0.3), Color.clear]),
                                center: .center,
                                startRadius: 30,
                                endRadius: 45
                            )
                        )
                        .frame(width: 90, height: 90)
                    
                    // 底部阴影增强立体感
                    Circle()
                        .fill(Color.black.opacity(0.2))
                        .frame(width: 72, height: 72)
                        .blur(radius: 4)
                        .offset(y: 2)
                        .mask(Circle().frame(width: 70, height: 70).offset(y: 2))
                    
                    // 黄色内圆（渐变色）- 增强对比度
                    Circle()
                        .fill(
                            LinearGradient(
                                stops: [
                                Gradient.Stop(color: Color(red: 0.98, green: 0.65, blue: 0.1), location: 0.24),
                                Gradient.Stop(color: Color(red: 0.97, green: 0.78, blue: 0), location: 0.41),
                                Gradient.Stop(color: Color(red: 1, green: 0.9, blue: 0.4), location: 0.64),
                                Gradient.Stop(color: Color(red: 1, green: 0.95, blue: 0.7), location: 0.75),
                                Gradient.Stop(color: Color(red: 1, green: 0.96, blue: 0.78), location: 0.83),
                                Gradient.Stop(color: Color(red: 1, green: 0.92, blue: 0.52), location: 1.00),
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 70, height: 70)
                    
                    // 边缘高光 - 更明显的边缘
                    Circle()
                        .stroke(
                            LinearGradient(
                                gradient: Gradient(colors: [Color.white.opacity(0.9), Color.white.opacity(0.2)]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 10
                        )
                        .frame(width: 70, height: 70)
                    
                    // 底部阴影增强立体感
                    Circle()
                        .stroke(Color.black.opacity(0.15), lineWidth: 4)
                        .blur(radius: 2)
                        .offset(x: 1, y: 1)
                        .mask(Circle().frame(width: 70, height: 70))
                    
                    // 高光效果 - 更亮更集中
                    Circle()
                        .fill(
                            RadialGradient(
                                gradient: Gradient(colors: [Color.white.opacity(0.95), Color.white.opacity(0.6), Color.clear]),
                                center: .topLeading,
                                startRadius: 1,
                                endRadius: 40
                            )
                        )
                        .frame(width: 68, height: 68)
                        .mask(
                            // 使高光只在左上部分显示
                            Circle()
                                .frame(width: 68, height: 68)
                                .offset(x: 10, y: 10)
                        )
                        .offset(x: -10, y: -10)
                    
                    // 小光点增强立体感
                    Circle()
                        .fill(Color.white.opacity(0.9))
                        .frame(width: 8, height: 8)
                        .offset(x: -20, y: -20)
                        .blur(radius: 2)
                    
                    // 白色加号 - 增加阴影
                    Image(systemName: "plus")
                        .font(.system(size: 50, weight: .bold))
                        .foregroundColor(.white)
                        .shadow(color: Color.black.opacity(0.4), radius: 2, x: 0, y: 2)
                         
                }
                .offset(y: -geometry.size.height * 0.03) // 向上偏移更多，使按钮更加凸出
                .contentShape(Circle()) // 确保点击区域为圆形
                .onTapGesture {
                    // 首先检查是否有正在进行的任务
                    if userState.hasActiveCreationTask() {
                        // 显示提示信息
                        toastMessage = "请等待之前的结果生成哦～"
                        showToast = true
                        return
                    }
                    
                    // 检查余额是否足够（至少2个捏币）
                    if userState.balance >= 2 {
                        // 显示弹窗，不改变选中的标签
                        userState.showAICreateSheet = true
                    } else {
                        // 显示余额不足提示
                        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                           let rootVC = windowScene.windows.first?.rootViewController {
                            let alert = UIAlertController(title: "余额不足", message: "进行AI创作需要至少2个捏币", preferredStyle: .alert)
                            alert.addAction(UIAlertAction(title: "确定", style: .default))
                            rootVC.present(alert, animated: true)
                        }
                    }
                }
                Spacer()
                
                // 我的按钮
                VStack(spacing: 4) {
                    Image(2 == selectedTabIndex ? navigationItems[2].icon.selected : navigationItems[2].icon.unselected)
                        .resizable()
                        .scaledToFit()
                        .frame(
                            width: geometry.size.width * 0.075,
                            height: geometry.size.height * 0.035
                        )
                    
                    Text(navigationItems[2].title)
                        .font(.system(
                            size: geometry.size.width * 0.032,
                            weight: .semibold
                        ))
                        .foregroundColor(2 == selectedTabIndex ? .black : Color(red: 0.27, green: 0.18, blue: 0.13))
                }
                .frame(maxWidth: .infinity)
                .onTapGesture {
                    selectedTabIndex = 2
                    userState.selectedTab = 2
                }
            }
            .padding(.horizontal, geometry.size.width * 0.05)
            .padding(.top, 30)
            .padding(.bottom, geometry.safeAreaInsets.bottom)
        }
        .frame(height: geometry.size.height * 0.074 + geometry.safeAreaInsets.bottom)
        .overlay(
            // 使用overlay添加Toast，而不是直接应用修饰符
            Group {
                if showToast {
                    VStack {
                        Spacer()
                        Text(toastMessage)
                            .font(Font.custom("PingFang SC", size: 14))
                            .foregroundColor(.white)
                            .padding(.horizontal, 20)
                            .padding(.vertical, 10)
                            .background(
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(Color.black.opacity(0.7))
                            )
                            .padding(.bottom, 100) // 确保Toast显示在导航栏上方
                            .transition(.opacity)
                            .onAppear {
                                // 2秒后自动隐藏Toast
                                DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                                    withAnimation {
                                        showToast = false
                                    }
                                }
                            }
                    }
                }
            }
        )
        .onAppear {
            // 确保初始状态同步
            selectedTabIndex = userState.selectedTab
        }
        .onChange(of: userState.selectedTab) { oldValue, newValue in
            // 当userState.selectedTab变化时，同步更新selectedTabIndex
            selectedTabIndex = newValue
        }
    }
}
