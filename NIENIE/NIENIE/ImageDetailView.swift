import SwiftUI
import Foundation
import Photos
import WebKit
import Combine

// 添加评论操作菜单枚举
enum CommentAction {
    case reply
    case delete
    case report
}

// 评论视图组件
struct CommentView: View {
    @EnvironmentObject var userState: UserState
    @ObservedObject var comment: CommentData // 修改为 @ObservedObject
    let level: Int
    let replyToUsername: String?
    let currentUserId: Int
    let isExpanded: Bool
    let onReply: (CommentData) -> Void
    let onDelete: (Int) -> Void
    let onToggleExpand: (Int) -> Void
    let onReport: (CommentData) -> Void
    let onShowProfile: (Int, String, String) -> Void // 新增回调
    
    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            // 评论头部
            HStack(alignment: .top) {
                // 用户头像
                Button(action: {
                    onShowProfile(comment.userId, comment.username, comment.avatar)
                }) {
                    Image(comment.avatar)
                        .resizable()
                        .scaledToFill()
                        .frame(width: 40, height: 40)
                        .clipShape(Circle())
                }
                .buttonStyle(PlainButtonStyle())
                
                VStack(alignment: .leading, spacing: 5) {
                    // 用户名和时间
                    HStack {
                        Text(comment.username)
                            .font(Font.custom("PingFang SC", size: 14).bold())
                            .foregroundColor(.black)
                        
                        Spacer()
                        
                        Text(formatCommentDate(comment.createdAt))
                            .font(Font.custom("PingFang SC", size: 12))
                            .foregroundColor(.gray)
                    }
                    
                    // 评论内容，如果是回复则显示"回复谁：内容"的格式
                    if level >= 1, let parentUser = replyToUsername {
                        Text("回复 @\(parentUser)：\(comment.content)")
                            .font(Font.custom("PingFang SC", size: 14))
                            .foregroundColor(.black)
                            .fixedSize(horizontal: false, vertical: true)
                            .onTapGesture {
                                // 点击评论内容直接回复
                                onReply(comment)
                            }
                    } else {
                        // 普通评论内容
                        Text(comment.content)
                            .font(Font.custom("PingFang SC", size: 14))
                            .foregroundColor(.black)
                            .fixedSize(horizontal: false, vertical: true)
                            .onTapGesture {
                                // 点击评论内容直接回复
                                onReply(comment)
                            }
                    }
                }
                .padding(.leading, 5)
            }
            .contentShape(Rectangle()) // 确保整个区域可点击
            .onLongPressGesture {
                // 触发震动反馈
                let generator = UIImpactFeedbackGenerator(style: .medium)
                generator.impactOccurred()
                
                // 显示操作菜单
                showActionMenu(comment: self.comment) // 使用 self.comment
            }
            
            // 如果有回复，显示"展示n条回复"按钮
            if let replies = comment.replies, !replies.isEmpty {
                Button(action: {
                    onToggleExpand(comment.commentId)
                }) {
                    Text(isExpanded ? "收起回复" : "展开 \(replies.count) 条回复")
                        .font(Font.custom("PingFang SC", size: 12))
                        .foregroundColor(.gray)
                }
            }
        }
        .padding(.leading, CGFloat(level) * 20) // 根据层级进行缩进
    }
    
    private func formatCommentDate(_ dateString: String) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
        
        guard let date = formatter.date(from: dateString) else {
            return dateString
        }
        
        let calendar = Calendar.current
        let now = Date()
        
        // 计算时间差
        let components = calendar.dateComponents([.year, .month, .day, .hour, .minute], from: date, to: now)
        
        if let year = components.year, year > 0 {
            return "\(year)年前"
        } else if let month = components.month, month > 0 {
            return "\(month)个月前"
        } else if let day = components.day, day > 0 {
            return "\(day)天前"
        } else if let hour = components.hour, hour > 0 {
            return "\(hour)小时前"
        } else if let minute = components.minute, minute > 0 {
            return "\(minute)分钟前"
        } else {
            return "刚刚"
        }
    }
}

struct ImageDetailView: View {
    @EnvironmentObject var userState: UserState
    @Environment(\.dismiss) var dismiss
    
    let imageId: Int
    @State private var image: ImageData? = nil
    @State private var foregroundPath: String? = nil
    @State private var isLoading = true
    @State private var errorMessage: String? = nil
    @State private var isLiked = false
    @State private var likeCount: Int = 0
    @State private var showImagePickerSheet = false
    
    // 评论相关状态
    @State private var comments: [CommentData] = []
    @State private var isLoadingComments = false
    @State private var commentText = ""
    @State private var showCommentInput = false
    @State private var replyingTo: CommentData? = nil
    @State private var currentImageIndex = 0 // 0: 合成效果图, 1: 背景图
    
    // 使用扁平化的评论列表来驱动UI，并确保状态更新
    @State private var displayComments: [DisplayComment] = []
    @State private var expandedComments: Set<Int> = [] // 追踪已展开的评论
    @State private var scrollToCommentId: Int? = nil // 用于滚动到新添加的评论
    @State private var showFullScreenImage = false // 全屏显示图片
    @State private var showImagePicker = false
    @State private var showCamera = false
    @State private var selectedImage: UIImage?
    @State private var showImageEditView = false // 新增：控制图片编辑视图的显示

    // 新增：通用信息提示弹窗
    @State private var showInfoAlert: Bool = false
    @State private var infoAlertTitle: String = ""
    @State private var infoAlertMessage: String = ""
    
    // 添加缩放和拖动相关状态
    @State private var currentScale: CGFloat = 1.0
    @State private var previousScale: CGFloat = 1.0
    @State private var currentOffset: CGSize = .zero
    @State private var previousOffset: CGSize = .zero
    
    // 添加Toast提示状态
    @State private var showToast = false
    @State private var toastMessage = ""
    
    // 自定义滑动返回交互状态
    @State private var dragTranslation: CGFloat = 0
    @State private var isDragging: Bool = false
    @State private var isDismissing: Bool = false
    
    // 添加评论举报相关状态
    @State private var showReportMenu = false
    @State private var reportingComment: CommentData? = nil
    
    // 添加评论操作菜单状态
    @State private var showActionMenu = false
    @State private var currentActionComment: CommentData? = nil
    
    // 新增：用于呈现用户个人资料页的状态
    @State private var showUserProfile = false
    @State private var selectedUserProfile: (userId: Int, username: String, avatar: String)? = nil
    
    // 在ImageDetailView中添加显示举报菜单的方法
    private func showReportMenu(comment: CommentData) {
        self.reportingComment = comment
        self.showReportMenu = true
    }
    
    // 删除showCommentActionMenu方法，我们将在CommentView中直接使用操作菜单

    // 在ImageDetailView类中添加一个集合来跟踪正在删除的评论ID
    @State private var deletingCommentIds: Set<Int> = []

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景色
                Color.white.ignoresSafeArea()
                //Color.white.ignoresSafeArea().opacity(isDragging || isDismissing ? 0 : 1)
                if isLoading {
                    VStack {
                        Spacer()
                        ProgressView("加载中...")
                            .progressViewStyle(CircularProgressViewStyle())
                            .scaleEffect(1.5)
                        Spacer()
                    }
                } else if let _ = errorMessage {
                    // 资源不可用时的提示界面
                    VStack(alignment: .leading) {
                        // 固定的顶部导航栏（仅包含返回按钮）
                        HStack {
                            Button(action: {
                                // 返回并发送刷新通知
                                NotificationCenter.default.post(
                                    name: NSNotification.Name("RefreshHomeImages"),
                                    object: nil
                                )
                                dismiss()
                            }) {
                                Image(systemName: "chevron.left")
                                    .foregroundColor(.black)
                                    .font(.system(size: 18))
                                    .padding(8)
                                    .background(Color.white.opacity(0.7))
                                    .clipShape(Circle())
                            }
                            Spacer()
                        }
                        .padding(.horizontal)
                        .padding(.top, 10)
                        .zIndex(1)
                        
                        Spacer()
                        
                        // 错误提示
                        HStack {
                            Spacer()
                            VStack(spacing: 20) {
                                Image(systemName: "photo.slash")
                                    .font(.system(size: 60))
                                    .foregroundColor(.gray)
                                
                                Text("您访问的资源不见了捏～")
                                    .font(Font.custom("PingFang SC", size: 18))
                                    .foregroundColor(.gray)
                            }
                            Spacer()
                        }
                        
                        Spacer()
                    }
                    .frame(width: geometry.size.width) // 确保整个视图占满宽度
                } else if let imageData = image {
                    VStack(spacing: 0) {
                        // 固定的顶部导航栏
                        HStack {
                            Button(action: {
                                dismiss()
                            }) {
                                Image(systemName: "chevron.left")
                                    .foregroundColor(.black)
                                    .background(Color.white.opacity(0.7))
                                    .clipShape(Circle())
                            }

                            Button(action: {
                                presentUserProfile(userId: imageData.userId, username: imageData.username, avatar: imageData.avatar)
                            }) {
                                HStack {
                                    // 头像
                                    Image(imageData.avatar)
                                        .resizable()
                                        .scaledToFill()
                                        .frame(width: 40, height: 40)
                                        .clipShape(Circle())
                                        .offset(x: 10) // 向右平移 10 个点
                                    
                                    // 用户名
                                    Text(imageData.username)
                                        .font(Font.custom("PingFang SC", size: 16))
                                        .foregroundColor(.black)
                                        .lineLimit(1)
                                        .offset(x: 10) // 向右平移 10 个点
                                }
                            }
                            .buttonStyle(PlainButtonStyle())

                            Spacer()
                            
                            // 创建时间
                            Text(formatDate(imageData.createdAt))
                                .font(Font.custom("PingFang SC", size: 12))
                                .foregroundColor(.gray)
                                .lineLimit(1)
                        }
                        .padding(.horizontal)
                        .padding(.vertical, 10)
                        .background(Color.white)
                        .zIndex(1) // 确保导航栏在最上层
                        
                        // 主要内容区域
                        ScrollViewReader { scrollProxy in
                            ScrollView {
                                VStack(spacing: 20) {
                                    // 图片展示 - 使用翻页效果
                                    TabView(selection: $currentImageIndex) {
                                        // 合成效果图
                                        AsyncImage(url: APIService.shared.getImageURL(filename: URL(string: imageData.storagePath)?.lastPathComponent ?? imageData.storagePath)) { phase in
                                            switch phase {
                                            case .empty:
                                                Rectangle()
                                                    .foregroundColor(.gray.opacity(0.3))
                                                    .aspectRatio(1.0, contentMode: .fit)
                                                    .overlay(
                                                        ProgressView()
                                                            .progressViewStyle(CircularProgressViewStyle())
                                                    )
                                            case .success(let displayImage):
                                                        displayImage
                                                            .resizable()
                                                            .aspectRatio(contentMode: .fit)
                                                            .onTapGesture {
                                                                showFullScreenImage = true
                                                            }
                                                    case .failure:
                                                        Rectangle()
                                                            .foregroundColor(.gray.opacity(0.3))
                                                            .aspectRatio(1.0, contentMode: .fit)
                                                            .overlay(
                                                                Image(systemName: "exclamationmark.triangle")
                                                                    .foregroundColor(.gray)
                                                            )
                                                    @unknown default:
                                                        Rectangle()
                                                            .foregroundColor(.gray.opacity(0.3))
                                                }
                                        }
                                        .frame(width: geometry.size.width - 32)
                                        .cornerRadius(10)
                                        .tag(0)
                                        
                                        // 背景图片
                                        if let foregroundPath = foregroundPath {
                                            AsyncImage(url: APIService.shared.getImageURL(filename: URL(string: foregroundPath)?.lastPathComponent ?? foregroundPath, isForeground: true)) { phase in
                                                switch phase {
                                                case .empty:
                                                    Rectangle()
                                                        .foregroundColor(.gray.opacity(0.3))
                                                        .aspectRatio(1.0, contentMode: .fit)
                                                        .overlay(
                                                            ProgressView()
                                                                .progressViewStyle(CircularProgressViewStyle())
                                                        )
                                                case .success(let displayImage):
                                                    displayImage
                                                        .resizable()
                                                        .aspectRatio(contentMode: .fit)
                                                        .onTapGesture {
                                                            showFullScreenImage = true
                                                        }
                                                case .failure:
                                                    Rectangle()
                                                        .foregroundColor(.gray.opacity(0.3))
                                                        .aspectRatio(1.0, contentMode: .fit)
                                                        .overlay(
                                                            Image(systemName: "exclamationmark.triangle")
                                                                .foregroundColor(.gray)
                                                        )
                                                @unknown default:
                                                    Rectangle()
                                                        .foregroundColor(.gray.opacity(0.3))
                                                }
                                            }
                                            .frame(width: geometry.size.width - 32)
                                            .cornerRadius(10)
                                            .tag(1)
                                        }
                                    }
                                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never)) // 隐藏页面指示器
                                    .frame(width: geometry.size.width, height: geometry.size.width)
                                    
                                    // 添加自定义指示点
                                    HStack(spacing: 10) {
                                        Circle()
                                            .fill(currentImageIndex == 0 ? Color.blue : Color.gray.opacity(0.5))
                                            .frame(width: 8, height: 8)
                                        
                                        if foregroundPath != nil {
                                            Circle()
                                                .fill(currentImageIndex == 1 ? Color.blue : Color.gray.opacity(0.5))
                                                .frame(width: 8, height: 8)
                                        }
                                    }
                                    .padding(.top, -15)
                                    
                                    // 添加标题和内容区域
                                    if let title = imageData.title, let content = imageData.content {
                                        VStack(alignment: .leading, spacing: 10) {
                                            // 标题
                                            Text(title)
                                                .font(Font.custom("PingFang SC", size: 18).bold())
                                                .foregroundColor(.black)
                                                .padding(.horizontal)
                                            
                                            // 内容
                                            Text(content)
                                                .font(Font.custom("PingFang SC", size: 16))
                                                .foregroundColor(.black.opacity(0.8))
                                                .fixedSize(horizontal: false, vertical: true)
                                                .padding(.horizontal)
                                        }
                                        .background(Color.white)
                                    }
                                    
                                    // 评论区
                                    VStack(alignment: .leading, spacing: 15) {
                                        Text("评论区")
                                            .font(Font.custom("PingFang SC", size: 18).bold())
                                            .padding(.horizontal)
                                        
                                        if isLoadingComments {
                                            HStack {
                                                Spacer()
                                                ProgressView()
                                                Spacer()
                                            }
                                            .padding(.vertical, 20)
                                        } else if displayComments.isEmpty {
                                            HStack {
                                                Spacer()
                                                Text("暂无评论，快来发表第一条评论吧")
                                                    .font(Font.custom("PingFang SC", size: 14))
                                                    .foregroundColor(.gray)
                                                Spacer()
                                            }
                                            .padding(.vertical, 20)
                                        } else {
                                            // 评论列表
                                            ForEach(displayComments) { displayComment in
                                                let comment = displayComment.commentData
                                                CommentView(
                                                    comment: comment,
                                                    level: displayComment.level,
                                                    replyToUsername: findParentUsername(for: displayComment),
                                                    currentUserId: userState.userId,
                                                    isExpanded: expandedComments.contains(comment.commentId),
                                                    onReply: { comment in
                                                        replyingTo = comment
                                                        showCommentInput = true
                                                    },
                                                    onDelete: { commentId in
                                                        deleteComment(commentId: commentId)
                                                    },
                                                    onToggleExpand: { commentId in
                                                        toggleCommentExpansion(commentId: commentId)
                                                    },
                                                    onReport: { comment in
                                                        showReportMenu(comment: comment)
                                                    },
                                                    onShowProfile: { userId, username, avatar in
                                                        presentUserProfile(userId: userId, username: username, avatar: avatar)
                                                    }
                                                )
                                                .id(comment.commentId) // 用于滚动定位
                                                .padding(.horizontal)
                                                .environmentObject(userState)
                                                
                                                if displayComment.id != displayComments.last?.id {
                                                    Divider()
                                                        .padding(.horizontal)
                                                }
                                            }
                                        }
                                    }
                                    .padding(.vertical, 10)
                                    .frame(width: geometry.size.width) // 确保评论区占满宽度
                                    
                                    // 底部留白，确保内容不被底部工具栏遮挡
                                    Spacer().frame(height: 60)
                                }
                                .padding(.bottom, 30)
                                .onChange(of: scrollToCommentId) { oldValue, newValue in
                                    if let id = newValue {
                                        // 使用延迟确保布局已更新
                                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                            withAnimation {
                                                // 滚动到指定评论
                                                scrollProxy.scrollTo(id, anchor: .top)
                                                scrollToCommentId = nil // 重置滚动ID
                                            }
                                        }
                                    }
                                }
                            }
                            .frame(width: geometry.size.width) // 确保滚动视图占满宽度
                        }
                        
                        // 底部工具栏
                        VStack(spacing: 0) {
                            Divider()
                            
                            HStack {
                                // 点赞按钮
                                Button(action: {
                                    likeImage()
                                }) {
                                    HStack(spacing: 5) {
                                        Image(isLiked ? "5-5" : "5-4")
                                            .resizable()
                                            .frame(width: 24, height: 24)
                                        
                                        Text("\(likeCount)")
                                            .font(Font.custom("PingFang SC", size: 14))
                                            .foregroundColor(.black)
                                    }
                                }
                                
                                Spacer()
                                
                                // 添加"利用该模版创作"按钮
                                Button(action: {
                                    // 首先检查是否有正在进行的任务
                                    if userState.hasActiveCreationTask() {
                                        // 显示提示信息
                                        toastMessage = "请等待之前的结果生成哦～"
                                        showToast = true
                                        
                                        // 2秒后自动隐藏Toast
                                        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                                            withAnimation {
                                                showToast = false
                                            }
                                        }
                                        
                                        return
                                    }
                                    
                                    // 检查余额是否足够（至少2个捏币）
                                    if userState.balance >= 2 {
                                        // 缓存当前背景图并标记来源为详情页
                                        if let foregroundPath = foregroundPath {
                                            userState.templateBackgroundPath = foregroundPath
                                            userState.isCreatingFromTemplate = true
                                            showImagePickerSheet = true
                                        }
                                    } else {
                                        // 显示余额不足提示
                                        toastMessage = "余额不足，进行AI创作需要至少2个捏币"
                                        showToast = true
                                        
                                        // 2秒后自动隐藏Toast
                                        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                                            withAnimation {
                                                showToast = false
                                            }
                                        }
                                    }
                                }) {
                                    HStack(spacing: 5) {
                                        Image("nie") 
                                            .resizable()
                                            .scaledToFit()
                                            .frame(width: 20, height: 20)
                                        
                                        Text("捏同款")
                                            .font(Font.custom("PingFang SC", size: 14))
                                            .foregroundColor(.black)
                                    }
                                    .padding(.horizontal, 10)
                                    .padding(.vertical, 5)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 15)
                                            .stroke(Color(red: 0.98, green: 0.85, blue: 0.37), lineWidth: 1.5)
                                    )
                                }
                                
                                Spacer()
                                
                                // 评论按钮
                                Button(action: {
                                    showCommentInput = true
                                    replyingTo = nil
                                }) {
                                    HStack(spacing: 5) {
                                        Image("mess")
                                            .resizable()
                                            .scaledToFit()
                                            .frame(width: 25, height: 25)
                                            .foregroundColor(.black)
                                        
                                        Text("评论")
                                            .font(Font.custom("PingFang SC", size: 14))
                                            .foregroundColor(.black)
                                    }
                                }
                            }
                            .padding(.horizontal)
                            .padding(.vertical, 10)
                            .background(Color.white)
                        }
                    }
                    .frame(width: geometry.size.width) // 确保整个内容视图占满宽度
                    
                    // 评论输入框
                    if showCommentInput {
                        CommentInputView(
                            isPresented: $showCommentInput,
                            commentText: $commentText,
                            replyingTo: replyingTo,
                            onSubmit: { text in
                                if let replyTo = replyingTo {
                                    addComment(content: text, parentCommentId: replyTo.commentId)
                                } else {
                                    addComment(content: text)
                                }
                            }
                        )
                    }
                    
                    // 全屏图片视图
                    if showFullScreenImage {
                        ZStack {
                            Color.black.opacity(0.9).ignoresSafeArea()
                            
                            // 只显示当前选中的图片，不使用TabView
                            if currentImageIndex == 0 {
                                // 合成效果图
                                AsyncImage(url: APIService.shared.getImageURL(filename: URL(string: imageData.storagePath)?.lastPathComponent ?? imageData.storagePath)) { phase in
                                    switch phase {
                                    case .empty:
                                        ProgressView()
                                            .progressViewStyle(CircularProgressViewStyle())
                                            .foregroundColor(.white)
                                    case .success(let displayImage):
                                        ZoomableImageView(
                                            image: displayImage,
                                            imageUrl: APIService.shared.getImageURL(filename: URL(string: imageData.storagePath)?.lastPathComponent ?? imageData.storagePath),
                                            showFullScreen: $showFullScreenImage
                                        )
                                    case .failure:
                                        Image(systemName: "exclamationmark.triangle")
                                            .foregroundColor(.white)
                                    @unknown default:
                                        EmptyView()
                                    }
                                }
                            } else if currentImageIndex == 1, let foregroundPath = foregroundPath {
                                // 背景图片
                                AsyncImage(url: APIService.shared.getImageURL(filename: URL(string: foregroundPath)?.lastPathComponent ?? foregroundPath, isForeground: true)) { phase in
                                    switch phase {
                                    case .empty:
                                        ProgressView()
                                            .progressViewStyle(CircularProgressViewStyle())
                                            .foregroundColor(.white)
                                    case .success(let displayImage):
                                        ZoomableImageView(
                                            image: displayImage,
                                            imageUrl: APIService.shared.getImageURL(filename: URL(string: foregroundPath)?.lastPathComponent ?? foregroundPath, isForeground: true),
                                            showFullScreen: $showFullScreenImage
                                        )
                                    case .failure:
                                        Image(systemName: "exclamationmark.triangle")
                                            .foregroundColor(.white)
                                    @unknown default:
                                        EmptyView()
                                    }
                                }
                            }
                        }
                        .zIndex(2) // 确保全屏视图在最上层
                    }
                }
                
                // 悬浮球视图 - 添加到ZStack的最后，确保它显示在最上层
                if userState.showFloatingBall {
                    FloatingBallView()
                        .environmentObject(userState)
                        .zIndex(100) // 确保悬浮球始终位于最上层
                }
                
                // 添加评论菜单容器
                CommentMenuContainer()
                    .environmentObject(userState)
                    .zIndex(200) // 确保菜单在最顶层
            }
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("DeleteComment"))) { notification in
                if let commentId = notification.userInfo?["commentId"] as? Int {
                    deleteComment(commentId: commentId)
                }
            }
            .onAppear {
                // 添加标志，避免重复加载数据
                if image == nil {
                    loadImageDetail()
                }
                if comments.isEmpty {
                    loadComments()
                }
                
                // 添加评论相关通知的监听
                setupNotificationObservers()
            }
            .onDisappear {
                // 移除通知观察者
                removeNotificationObservers()
            }
            // 添加自定义滑动返回手势及动效
            .gesture(
                DragGesture()
                    .onChanged { value in
                        guard !showFullScreenImage && !showCommentInput else { return }
                        let translationX = value.translation.width
                        if translationX > 0 {
                            isDragging = true
                            dragTranslation = translationX
                        }
                    }
                    .onEnded { value in
                        guard !showFullScreenImage && !showCommentInput else { return }
                        let screenWidth = geometry.size.width
                        let threshold = screenWidth * 0.2
                        if value.translation.width > threshold {
                            // 开始退出动画
                            isDismissing = true
                            withAnimation(.easeOut(duration: 0.2)) {
                                dragTranslation = screenWidth
                            }
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                                dismiss()
                            }
                        } else {
                            // 取消, 回到初始状态
                            withAnimation(.spring()) {
                                dragTranslation = 0
                            }
                            isDragging = false
                        }
                    }
            )
            // 视图偏移
            .offset(x: dragTranslation)
            // 视图缩放
            .scaleEffect(isDismissing ? 0 : (isDragging ? (1 - dragTranslation / geometry.size.width) : 1))
            .animation(.interactiveSpring(response: 0.3, dampingFraction: 0.8), value: dragTranslation)
            .background(ClearBackgroundView())
        }
        .edgesIgnoringSafeArea(showFullScreenImage ? .all : []) // 全屏模式忽略安全区域
        .alert(infoAlertTitle, isPresented: $showInfoAlert) {
            Button("确定", role: .cancel) {}
        } message: {
            Text(infoAlertMessage)
        }
        
        .fullScreenCover(isPresented: $showImagePicker) {
            ImagePickerView(sourceType: .photoLibrary) { image in
                if let image = image {
                    self.selectedImage = image
                    handleSelectedImage()
                }
            }
        }
        .fullScreenCover(isPresented: $showCamera) {
            ImagePickerView(sourceType: .camera) { image in
                if let image = image {
                    self.selectedImage = image
                    handleSelectedImage()
                }
            }
        }
        .fullScreenCover(isPresented: $showImageEditView) {
            if let image = selectedImage {
                ImageEditView(image: image)
                    .environmentObject(userState)
            }
        }
        .overlay {
            // 将ImagePickerSheet放在overlay中，确保不影响主视图布局
            if showImagePickerSheet {
                ImagePickerSheet(
                    isPresented: $showImagePickerSheet,
                    onCameraSelected: {
                        showImagePickerSheet = false // 关闭底部弹出菜单
                        showCamera = true
                    }, 
                    onPhotoLibrarySelected: {
                        showImagePickerSheet = false // 关闭底部弹出菜单
                        showImagePicker = true
                    }
                )
            }
            
            // 自定义Toast显示
            if showToast {
                VStack {
                    Spacer()
                    Text(toastMessage)
                        .font(Font.custom("PingFang SC", size: 14))
                        .foregroundColor(.white)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 10)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(Color.black.opacity(0.7))
                        )
                        .padding(.bottom, 50)
                }
                .transition(.opacity)
                .animation(.easeInOut, value: showToast)
                .zIndex(999)
            }
        }
    }
    
    private func loadImageDetail() {
        isLoading = true
        
        APIService.shared.getImageDetail(imageId: imageId, userId: userState.userId) { result in
            DispatchQueue.main.async {
                isLoading = false
                
                switch result {
                case .success(let (imageData, bgPath)):
                    self.image = imageData
                    self.foregroundPath = bgPath  // 这里实际上是背景图路径
                    self.likeCount = imageData.likeCount
                    self.isLiked = imageData.liked
                case .failure(_):
                    self.errorMessage = "您访问的资源不见了捏～"
                }
            }
        }
    }
    
    private func likeImage() {
        guard userState.userId > 0 else { return }
        
        APIService.shared.likeImage(imageId: imageId, userId: userState.userId) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let (newLikeCount, liked)):
                    self.isLiked = liked
                    self.likeCount = newLikeCount
                    
                    // 发送通知刷新首页和个人页中的点赞状态
                    NotificationCenter.default.post(
                        name: NSNotification.Name("RefreshHomeImages"),
                        object: nil,
                        userInfo: ["imageId": imageId, "liked": liked, "likeCount": newLikeCount]
                    )
                    
                    // 如果取消点赞，发送额外的通知以立即从喜欢列表中移除
                    if !liked {
                        NotificationCenter.default.post(
                            name: NSNotification.Name("RemoveLikedImage"),
                            object: nil, 
                            userInfo: ["imageId": imageId]
                        )
                    }
                case .failure(let error):
                    print("详情页点赞操作失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    // 加载评论
    private func loadComments(completion: (() -> Void)? = nil) {
        isLoadingComments = true
        
        APIService.shared.getImageComments(imageId: imageId, userId: userState.userId) { result in
            DispatchQueue.main.async {
                isLoadingComments = false
                
                switch result {
                case .success(let comments):
                    self.comments = comments
                    self.displayComments = flattenComments(comments: self.comments)
                case .failure(let error):
                    print("加载评论失败: \(error.localizedDescription)")
                }
                
                // 调用完成回调
                completion?()
            }
        }
    }
    
    // 添加评论
    private func addComment(content: String, parentCommentId: Int? = nil) {
        guard !content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        // 如果是回复评论，并且该评论有子评论但未展开，先展开它
        if let parentId = parentCommentId {
            // 检查该评论是否有子评论但未展开
            let parentComment = comments.first { $0.commentId == parentId }
            if let parent = parentComment, 
               let replies = parent.replies, 
               !replies.isEmpty && 
               !expandedComments.contains(parentId) {
                // 自动展开该评论
                expandedComments.insert(parentId)
                // 重新生成扁平化列表以反映展开状态
                self.displayComments = flattenComments(comments: self.comments)
            }
        }
        
        APIService.shared.addComment(
            imageId: imageId,
            userId: userState.userId,
            content: content,
            parentCommentId: parentCommentId
        ) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let newComment):
                    
                    // ---- 本地数据结构更新 ----
                    if let parentId = parentCommentId {
                        // 在层级数据中找到父评论并添加回复
                        if let parentComment = findComment(in: self.comments, withId: parentId) {
                            if parentComment.replies == nil {
                                parentComment.replies = []
                            }
                            parentComment.replies?.insert(newComment, at: 0)
                            
                            // 确保父评论处于展开状态
                            self.expandedComments.insert(parentId)
                            
                            // 重新生成扁平化列表以触发UI更新
                            self.displayComments = flattenComments(comments: self.comments)
                            // 滚动到父评论
                            self.scrollToCommentId = parentId
                        }
                    } else {
                        // 一级评论
                        self.comments.insert(newComment, at: 0)
                        self.displayComments.insert(DisplayComment(commentData: newComment, level: 0), at: 0)
                        self.scrollToCommentId = newComment.commentId
                    }
                    
                    // 重置输入框状态
                    self.commentText = ""
                    self.showCommentInput = false
                    self.replyingTo = nil
                    
                case .failure:
                    
                    // 检查这是否是因父评论不存在而导致的失败
                    if let parentId = parentCommentId {
                        // 假设任何向已删除评论添加回复的尝试都会失败
                        self.infoAlertTitle = "评论失败"
                        self.infoAlertMessage = "该评论已被对方删除，无法回复。"
                        self.showInfoAlert = true
                        
                        // 从UI中移除这条失效的评论及其所有子评论
                        self.removeCommentFromDataSource(commentId: parentId)
                        self.displayComments = flattenComments(comments: self.comments)
                    } else {
                        // 其他类型的添加评论失败
                        self.infoAlertTitle = "评论失败"
                        self.infoAlertMessage = "无法发送评论，请稍后再试。"
                        self.showInfoAlert = true
                    }
                    
                    // 清理状态
                    self.commentText = ""
                    self.showCommentInput = false
                    self.replyingTo = nil
                }
            }
        }
    }
    
    // 删除评论
    private func deleteComment(commentId: Int) {
        // 检查该评论是否正在删除中
        guard !deletingCommentIds.contains(commentId) else {
            print("评论 \(commentId) 正在删除中，忽略重复请求")
            return
        }
        
        // 将评论ID添加到正在删除的集合中
        deletingCommentIds.insert(commentId)
        
        APIService.shared.deleteComment(commentId: commentId, userId: userState.userId) { result in
            DispatchQueue.main.async {
                // 无论结果如何，从正在删除的集合中移除该评论ID
                self.deletingCommentIds.remove(commentId)
                
                switch result {
                case .success(_):
                    // ---- 本地数据结构更新 ----
                    self.removeCommentFromDataSource(commentId: commentId)
                    // 如果该评论之前处于展开状态，需要同步移除
                    self.expandedComments.remove(commentId)
                    // 重新生成扁平化数据
                    self.displayComments = flattenComments(comments: self.comments)

                case .failure:
                    // 如果删除失败，很可能是因为它已不存在。
                    // 无论如何，都应该从UI中移除它，并提示用户。
                    self.infoAlertTitle = "操作失败"
                    self.infoAlertMessage = "该评论可能已被他人删除。"
                    self.showInfoAlert = true
                    // 同样在本地移除，保持一致性
                    self.removeCommentFromDataSource(commentId: commentId)
                    self.expandedComments.remove(commentId)
                    self.displayComments = flattenComments(comments: self.comments)
                }
            }
        }
    }

    // MARK: - Helper Functions
    
    // 递归查找指定ID的评论
    private func findComment(in comments: [CommentData], withId commentId: Int) -> CommentData? {
        for comment in comments {
            if comment.commentId == commentId {
                return comment
            }
            if let foundInReplies = findComment(in: comment.replies ?? [], withId: commentId) {
                return foundInReplies
            }
        }
        return nil
    }

    // 递归从数据源中删除评论
    private func removeCommentFromDataSource(commentId: Int) {
        // 尝试在一级评论中删除
        self.comments.removeAll { $0.commentId == commentId }
        
        // 递归地在所有子评论中删除
        for comment in self.comments {
            removeCommentRecursive(from: comment, withId: commentId)
        }
    }

    private func removeCommentRecursive(from comment: CommentData, withId commentId: Int) {
        if let replies = comment.replies {
            // 先在子评论中递归删除
            for reply in replies {
                removeCommentRecursive(from: reply, withId: commentId)
            }
            // 然后删除当前层的匹配项
            comment.replies?.removeAll { $0.commentId == commentId }
        }
    }

    // 将层级评论数据扁平化为列表
    private func flattenComments(comments: [CommentData], level: Int = 0) -> [DisplayComment] {
        var flatList: [DisplayComment] = []
        for comment in comments {
            flatList.append(DisplayComment(commentData: comment, level: level))
            if let replies = comment.replies, expandedComments.contains(comment.commentId) {
                flatList.append(contentsOf: flattenComments(comments: replies, level: level + 1))
            }
        }
        return flatList
    }
    
    // 切换评论的展开/折叠状态，并刷新列表
    private func toggleCommentExpansion(commentId: Int) {
        if expandedComments.contains(commentId) {
            expandedComments.remove(commentId)
        } else {
            expandedComments.insert(commentId)
        }
        // 重新生成扁平化列表以反映展开/折叠状态
        self.displayComments = flattenComments(comments: self.comments)
    }
    
    // 查找父评论的用户名
    private func findParentUsername(for dc: DisplayComment) -> String? {
        // 根据用户要求，三级及以上评论显示回复谁 (level 0 -> 一级, level 1 -> 二级, level 2 -> 三级)
        // level >= 1 的评论是回复，都应该显示回复对象
        if dc.level < 1 { return nil }
        
        guard let parentId = dc.commentData.parentCommentId else { return nil }
        
        // 我们需要在原始的、完整的、层级的评论数据中查找，而不是在可能不完整的扁平列表中
        return findUsername(for: parentId, in: self.comments)
    }

    private func findUsername(for commentId: Int, in comments: [CommentData]) -> String? {
        for comment in comments {
            if comment.commentId == commentId {
                return comment.username
            }
            if let replies = comment.replies, let username = findUsername(for: commentId, in: replies) {
                return username
            }
        }
        return nil
    }

    private func formatDate(_ dateString: String) -> String {
        // 简单格式化日期，可根据实际日期格式调整
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
        
        if let date = formatter.date(from: dateString) {
            formatter.dateFormat = "yyyy-MM-dd HH:mm"
            return formatter.string(from: date)
        }
        
        return dateString
    }
    
    // 添加处理选择图片的函数
    private func handleSelectedImage() {
        guard let image = selectedImage else {
            return
        }

        // 在模版创作模式下设置特殊的导航标志
        if userState.isCreatingFromTemplate {
            userState.selectedCharacterImage = image
            userState.navigateToImageAdjustFromTemplate = true
        }

        // 触发ImageEditView的显示
        showImageEditView = true
    }
    
    // 使用UIKit手动弹出用户个人资料页
    private func presentUserProfile(userId: Int, username: String, avatar: String) {
        let userProfileView = UserProfileView(
            profileUserId: userId,
            profileUsername: username,
            profileAvatar: avatar
        ).environmentObject(userState)

        let hostingController = UIHostingController(rootView: userProfileView)
        hostingController.modalPresentationStyle = .fullScreen

        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootViewController = windowScene.windows.first?.rootViewController {
            var topController = rootViewController
            while let presentedViewController = topController.presentedViewController {
                topController = presentedViewController
            }
            topController.present(hostingController, animated: true, completion: nil)
        }
    }

    // 添加显示图片编辑界面的函数
    private func presentImageEditView(image: UIImage) {
        // 在模版创作模式下设置特殊的导航标志
        if userState.isCreatingFromTemplate {
            // 将选中的图片设置为人物图
            userState.selectedCharacterImage = image
            // 设置导航标志，指示从模版创作直接进入图像调整页面
            userState.navigateToImageAdjustFromTemplate = true
        }
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootViewController = windowScene.windows.first?.rootViewController {
            
            // 检查当前呈现的控制器，如果有，获取当前控制器
            var currentController = rootViewController
            while let presented = currentController.presentedViewController {
                currentController = presented
            }
            
            // 关闭当前正在显示的任何模态视图，以确保新的视图能够显示
            currentController.dismiss(animated: true) {
                // 创建ImageEditView
                let hostingController = UIHostingController(rootView: 
                    ImageEditView(image: image)
                        .environmentObject(userState)
                )
                hostingController.modalPresentationStyle = .fullScreen
                
                // 从根控制器呈现图片编辑页面
                rootViewController.present(hostingController, animated: true) {
                }
            }
        } 
    }
    
    // 添加通知观察者设置方法
    private func setupNotificationObservers() {
        // 监听回复评论的通知
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("ReplyToComment"),
            object: nil,
            queue: .main
        ) { notification in
            if let comment = notification.userInfo?["comment"] as? CommentData {
                self.replyingTo = comment
                self.showCommentInput = true
            }
        }
    }
    
    // 移除通知观察者
    private func removeNotificationObservers() {
        NotificationCenter.default.removeObserver(self, name: NSNotification.Name("ReplyToComment"), object: nil)
    }

    // MARK: - Helper Functions

    // 递归：向指定父评论插入子评论
    private func insertReply(into comments: [CommentData], parentId: Int, reply: CommentData) -> [CommentData] {
        return comments.map { comment in
            let newComment = comment
            
            // 如果当前评论是目标父评论
            if newComment.commentId == parentId {
                if newComment.replies == nil {
                    newComment.replies = []
                }
                newComment.replies?.insert(reply, at: 0)
                return newComment
            }
            
            // 如果不是，则递归到其子评论中查找
            if let subReplies = newComment.replies {
                newComment.replies = insertReply(into: subReplies, parentId: parentId, reply: reply)
            }
            
            return newComment
        }
    }

    // 递归：移除指定评论（及其子孙）
    private func removeComment(from comments: [CommentData], commentId: Int) -> [CommentData] {
        return comments.compactMap { comment -> CommentData? in
            // 如果当前评论是要删除的评论，则返回nil以将其过滤掉
            if comment.commentId == commentId {
                return nil
            }
            
            // 否则，保留该评论，并对其子评论进行递归删除
            let newComment = comment
            if let subReplies = newComment.replies {
                newComment.replies = removeComment(from: subReplies, commentId: commentId)
            }
            
            return newComment
        }
    }
}

// 用于设置模态视图背景透明的辅助视图
struct ClearBackgroundView: UIViewRepresentable {
    func makeUIView(context: Context) -> UIView {
        let view = UIView()
        Task {
            // 延迟以确保视图层级已建立
            try? await Task.sleep(nanoseconds: 1_000_000)
            view.superview?.superview?.backgroundColor = .clear
        }
        return view
    }

    func updateUIView(_ uiView: UIView, context: Context) {}
}

// 扁平化评论的数据结构
struct DisplayComment: Identifiable, Equatable {
    let id = UUID()
    // 使用 ObservableObject，以便在扁平化列表中也能观察变化
    @ObservedObject var commentData: CommentData
    var level: Int

    static func == (lhs: DisplayComment, rhs: DisplayComment) -> Bool {
        lhs.id == rhs.id
    }
}

// 修改CustomTextEditor，添加自动获取焦点功能
struct CustomTextEditor: UIViewRepresentable {
    @Binding var text: String
    var onSubmit: () -> Void
    var autoFocus: Bool = false // 添加自动获取焦点参数
    
    func makeUIView(context: Context) -> UITextView {
        let textView = UITextView()
        textView.font = UIFont.systemFont(ofSize: 16)
        textView.backgroundColor = .clear
        textView.delegate = context.coordinator
        textView.isScrollEnabled = true
        textView.returnKeyType = .send
        
        // 如果需要自动获取焦点，延迟一点执行
        if autoFocus {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                textView.becomeFirstResponder()
            }
        }
        
        return textView
    }
    
    func updateUIView(_ uiView: UITextView, context: Context) {
        uiView.text = text
        
        // 更新时检查是否需要获取焦点
        if autoFocus && !context.coordinator.focusRequested {
            context.coordinator.focusRequested = true
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                uiView.becomeFirstResponder()
            }
        }
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UITextViewDelegate {
        var parent: CustomTextEditor
        var focusRequested = false
        
        init(_ parent: CustomTextEditor) {
            self.parent = parent
        }
        
        func textViewDidChange(_ textView: UITextView) {
            parent.text = textView.text
        }
        
        func textView(_ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
            // 当用户点击键盘上的发送按钮时，text会是"\n"
            if text == "\n" {
                // 如果输入框不为空，则提交
                if !textView.text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    parent.onSubmit()
                }
                return false // 不插入换行符
            }
            return true
        }
    }
}

// 评论输入视图
struct CommentInputView: View {
    @Binding var isPresented: Bool
    @Binding var commentText: String
    let replyingTo: CommentData?
    let onSubmit: (String) -> Void
    
    var body: some View {
        ZStack {
            // 半透明背景
            Color.black.opacity(0.3)
                .ignoresSafeArea()
                .onTapGesture {
                    isPresented = false
                }
            
            // 输入框容器
            VStack(spacing: 0) {
                Spacer()
                
                VStack(spacing: 10) {
                    // 回复提示（如果有）
                    if let replyTo = replyingTo {
                        HStack {
                            Text("回复 \(replyTo.username):")
                                .font(Font.custom("PingFang SC", size: 14))
                                .foregroundColor(.gray)
                            
                            Spacer()
                            
                            Button(action: {
                                isPresented = false
                            }) {
                                Image(systemName: "xmark")
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding(.horizontal, 15)
                        .padding(.top, 10)
                    } else {
                        HStack {
                            Text("发表评论")
                                .font(Font.custom("PingFang SC", size: 14))
                                .foregroundColor(.gray)
                            
                            Spacer()
                            
                            Button(action: {
                                isPresented = false
                            }) {
                                Image(systemName: "xmark")
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding(.horizontal, 15)
                        .padding(.top, 10)
                    }
                    
                    // 输入框和发送按钮
                    HStack(spacing: 10) {
                        // 使用自定义TextEditor替代原来的TextEditor
                        ZStack(alignment: .leading) {
                            if commentText.isEmpty {
                                Text("说点什么...")
                                    .foregroundColor(.gray.opacity(0.8))
                                    .padding(.leading, 10)
                                    .padding(.top, 10)
                            }
                            
                            CustomTextEditor(text: $commentText, onSubmit: {
                                onSubmit(commentText)
                            }, autoFocus: true) // 添加自动获取焦点参数
                            .frame(minHeight: 40, maxHeight: 100)
                        }
                        .padding(5)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(20)
                        
                        // 发送按钮
                        Button(action: {
                            onSubmit(commentText)
                        }) {
                            Text("发送")
                                .font(Font.custom("PingFang SC", size: 14))
                                .foregroundColor(.white)
                                .padding(.horizontal, 15)
                                .padding(.vertical, 8)
                                .background(Color.yellow)
                                .cornerRadius(20)
                        }
                        .disabled(commentText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                        .opacity(commentText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? 0.5 : 1.0)
                    }
                    .padding(.horizontal, 15)
                    .padding(.bottom, 15)
                }
                .background(Color.white)
                .cornerRadius(10, corners: [.topLeft, .topRight])
            }
        }
        .onAppear {
            // 修改UI元素的语言为中文
            UITextView.appearance().overrideUserInterfaceStyle = .light
            
            // 通过修改应用语言环境来确保菜单项是中文
            let languages = UserDefaults.standard.object(forKey: "AppleLanguages") as? [String]
            if let languages = languages, !languages.contains("zh-Hans") && !languages.contains("zh-CN") {
                // 临时保存当前语言设置
                UserDefaults.standard.set(["zh-Hans", "en"], forKey: "AppleLanguages")
            }
        }
    }
}

// 可缩放的图片视图
struct ZoomableImageView: View {
    let image: Image
    let imageUrl: URL?  // 添加URL参数用于保存图片
    @Binding var showFullScreen: Bool

    // 缩放和拖动状态
    @State private var currentScale: CGFloat = 1.0
    @State private var lastScale: CGFloat = 1.0
    @State private var currentOffset: CGSize = .zero
    @State private var lastOffset: CGSize = .zero
    
    // 保存图片相关状态
    @State private var showSaveAlert = false
    @State private var isSaving = false
    @State private var saveSuccessMessage: String?
    
    // 重置状态
    private func resetState() {
        withAnimation(.spring()) {
            currentScale = 1.0
            lastScale = 1.0
            currentOffset = .zero
            lastOffset = .zero
        }
    }

    var body: some View {
        // --- 手势定义 ---

        // 1. 拖动手势 (用于平移)
        let dragGesture = DragGesture()
            .onChanged { value in
                // 实时更新偏移量
                currentOffset = CGSize(
                    width: lastOffset.width + value.translation.width,
                    height: lastOffset.height + value.translation.height
                )
            }
            .onEnded { value in
                // 保存最终偏移量
                lastOffset = currentOffset
            }
        
        // 2. 缩放手势
        let magnificationGesture = MagnificationGesture()
            .onChanged { value in
                // 实时更新缩放比例
                let delta = value / lastScale
                lastScale = value
                currentScale *= delta
            }
            .onEnded { value in
                // 保存最终缩放比例
                lastScale = 1.0
            }

        // 3. 长按手势 (用于保存), 设置最大移动距离以允许手指轻微抖动
        let longPressGesture = LongPressGesture(minimumDuration: 0.5, maximumDistance: 20)
            .onEnded { _ in
                // 触发震动反馈
                let feedback = UIImpactFeedbackGenerator(style: .heavy)
                feedback.impactOccurred()
                // 显示保存对话框
                showSaveAlert = true
            }

        // 4. 双击手势 (用于重置)
        let doubleTapGesture = TapGesture(count: 2)
            .onEnded {
                resetState()
            }
        
        // 5. 单击手势 (用于关闭)
        let singleTapGesture = TapGesture(count: 1)
            .onEnded {
                showFullScreen = false
            }

        // --- 手势组合 ---
        let panAndZoomGesture = dragGesture.simultaneously(with: magnificationGesture)
        let primaryGesture = longPressGesture.exclusively(before: panAndZoomGesture)
        let tapGestures = doubleTapGesture.exclusively(before: singleTapGesture)


        return image
            .resizable()
            .scaledToFit()
            .scaleEffect(currentScale)
            .offset(currentOffset)
            // 组合手势以实现正确的优先级和行为
            .gesture(primaryGesture)
            .gesture(tapGestures)
            .alert("保存图片", isPresented: $showSaveAlert) {
                Button("取消", role: .cancel) {}
                Button("保存到相册") {
                    saveImageToPhotos()
                }
            } message: {
                Text("是否将当前图片保存到相册？")
            }
            .overlay {
                ZStack {
                    if isSaving {
                        ProgressView("保存中...")
                            .padding()
                            .background(Color.black.opacity(0.7))
                            .foregroundColor(.white)
                            .cornerRadius(10)
                    }
                    
                    if let message = saveSuccessMessage {
                        Text(message)
                            .padding()
                            .background(Color.black.opacity(0.7))
                            .foregroundColor(.white)
                            .cornerRadius(10)
                            .onAppear {
                                DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                                    withAnimation {
                                        saveSuccessMessage = nil
                                    }
                                }
                            }
                    }
                }
            }
    }
    
    // 添加保存图片到相册的方法
    private func saveImageToPhotos() {
        guard let imageUrl = imageUrl else { return }
        
        isSaving = true
        
        // 在后台线程下载和保存图片
        DispatchQueue.global(qos: .userInitiated).async {
            do {
                // 下载图片数据
                let imageData = try Data(contentsOf: imageUrl)
                
                // 处理webp格式：先将webp转换为UIImage，再转为PNG或JPEG格式保存
                if let uiImage = UIImage(data: imageData) {
                    // 转换为jpeg格式数据（确保兼容性）
                    if let jpegData = uiImage.jpegData(compressionQuality: 1.0) {
                        // 保存到相册
                        PHPhotoLibrary.shared().performChanges({
                            let creationRequest = PHAssetCreationRequest.forAsset()
                            creationRequest.addResource(with: .photo, data: jpegData, options: nil)
                        }) { success, error in
                            DispatchQueue.main.async {
                                isSaving = false
                                
                                if success {
                                    // 成功时显示简单的文字提示
                                    saveSuccessMessage = "保存成功"
                                } else {
                                    // 失败时仍显示详细的错误信息对话框
                                    showSaveErrorAlert(message: "保存失败：\(error?.localizedDescription ?? "未知错误")")
                                }
                            }
                        }
                    } else {
                        DispatchQueue.main.async {
                            isSaving = false
                            showSaveErrorAlert(message: "无法转换图片格式")
                        }
                    }
                } else {
                    DispatchQueue.main.async {
                        isSaving = false
                        showSaveErrorAlert(message: "无法处理图片格式")
                    }
                }
            } catch {
                DispatchQueue.main.async {
                    isSaving = false
                    showSaveErrorAlert(message: "下载图片失败：\(error.localizedDescription)")
                }
            }
        }
    }
    
    // 显示保存错误提示
    private func showSaveErrorAlert(message: String) {
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootVC = windowScene.windows.first?.rootViewController {
            let alert = UIAlertController(title: "保存失败", message: message, preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            rootVC.present(alert, animated: true)
        }
    }
}

struct ImageDetailView_Previews: PreviewProvider {
    static var previews: some View {
        ImageDetailView(imageId: 1)
            .environmentObject(UserState())
    }
} 
