import SwiftUI

struct ProfileView: View {
    @EnvironmentObject var userState: UserState
    @State private var selectedTabIndex: Int = 2
    @State private var selectedContentTab: Int = 0
    @State private var userImages: [ImageData] = []
    @State private var likedImages: [ImageData] = []
    @State private var isLoading = false
    @State private var isLoadingLiked = false
    @State private var errorMessage: String? = nil
    @State private var likedErrorMessage: String? = nil
    @State private var likedImagesSkip = 0
    @State private var userImagesSkip = 0
    @State private var hasMoreLikedImages = true
    @State private var hasMoreUserImages = true
    @State private var isLoadingMoreLiked = false
    @State private var isLoadingMoreUserImages = false
    @State private var needRefreshUserImages = true
    @State private var needRefreshLikedImages = true
    @State private var lastLoadMoreUserImagesTime = Date(timeIntervalSince1970: 0) // 上次加载更多用户图片的时间
    @State private var lastLoadMoreLikedImagesTime = Date(timeIntervalSince1970: 0) // 上次加载更多喜欢图片的时间
    @State private var showToast = false // 控制提示框显示
    
    // 添加设置菜单相关状态
    @State private var showSettingsMenu = false
    @State private var showLogoutAlert = false
    @State private var navigateToSelectView = false
    
    // 超分功能相关状态
    @State private var showSuperResolutionView = false

    // 补光功能相关状态
    @State private var showDepthLightingView = false

    // 贴纸功能相关状态
    @State private var showStickerView = false
    
    // 为作品和喜欢列表添加唯一ID，用于强制刷新
    @State private var worksViewId = UUID()
    @State private var likedViewId = UUID()
    
    // 显示临时提示的函数
    private func showTemporaryToast() {
        withAnimation {
            showToast = true
        }
        
        // 2秒后自动隐藏
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            withAnimation {
                self.showToast = false
            }
        }
    }
    
    // 分离作品到左右两列
    private var leftColumnWorks: [ImageData] {
        userImages.enumerated().filter { $0.offset % 2 == 0 }.map { $0.element }
    }
    
    private var rightColumnWorks: [ImageData] {
        userImages.enumerated().filter { $0.offset % 2 == 1 }.map { $0.element }
    }
    
    // 分离喜欢的作品到左右两列
    private var leftColumnLiked: [ImageData] {
        likedImages.enumerated().filter { $0.offset % 2 == 0 }.map { $0.element }
    }
    
    private var rightColumnLiked: [ImageData] {
        likedImages.enumerated().filter { $0.offset % 2 == 1 }.map { $0.element }
    }
    
    // 定义网格布局
    private var gridLayout: [GridItem] {
        [
            GridItem(.flexible(), spacing: 16),
            GridItem(.flexible(), spacing: 16)
        ]
    }
    
    // 退出登录处理函数
    private func handleLogout() {
        // 清空用户信息和创作记录
        userState.logout()
        userState.creationRecords = [] // 清空创作记录
        navigateToSelectView = true
    }
    
    var body: some View {
        
            GeometryReader { geometry in
            ZStack(alignment: .top) {    
                
             
                
                // 1. 内容区域
                VStack(spacing: 0) {
                    // 顶部"个人中心"标题和设置图标
                    ZStack {
                        // 中间标题
                        Text("个人中心")
                            .font(Font.custom("PingFang SC", size: 20).weight(.bold))
                            .foregroundColor(.black)
                            .frame(maxWidth: .infinity)
                        
                        // 右侧图标
                        HStack(spacing: 16) {
                            Spacer()
                            
                            // 聊天图标移到设置图标左侧
                            NavigationLink(destination: MessageView().navigationBarHidden(true)) {
                                ZStack {
                                    Image("message")
                                        .font(.system(size: 28))
                                        .foregroundColor(.black)

                                    // 未读消息数量badge - 使用绝对定位避免影响布局
                                    if userState.unreadMessageCount > 0 {
                                        Text(userState.unreadMessageCount > 99 ? "99+" : "\(userState.unreadMessageCount)")
                                            .font(.system(size: 10, weight: .bold))
                                            .foregroundColor(.white)
                                            .frame(minWidth: 16, minHeight: 16)
                                            .background(Color.red)
                                            .clipShape(Circle())
                                            .position(x: 20, y: 6) // 绝对定位到右上角
                                    }
                                }
                                .frame(width: 28, height: 28) // 固定frame确保布局稳定
                            }
                            .simultaneousGesture(TapGesture().onEnded {
                                // 点击聊天图标时清空未读消息数量
                                userState.clearUnreadMessageCount()
                            })
                            
                            // 设置图标
                            Button(action: {
                                withAnimation {
                                    showSettingsMenu = true
                                }
                            }) {
                                ZStack {
                                    // 外部齿轮
                                    Image(systemName: "gearshape")
                                        .font(.system(size: 26))
                                        .foregroundColor(.black)
                                    
                                    // 内部小圆点
                                    Circle()
                                        .fill(Color.yellow)
                                        .frame(width: 10, height: 10)
                                }
                            }
                        }
                        .padding(.trailing, 16)
                    }
                    .padding(.top, 16)
                    
                    // 用户信息区域
                    HStack(alignment: .top, spacing: 16) {
                        // 直接使用本地资源加载头像
                        Image(userState.avatar)
                            .resizable()
                            .scaledToFill()
                        .frame(width: 70, height: 70)
                        .clipShape(Circle())
                        .overlay(Circle().stroke(Color.white, lineWidth: 2))
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text(userState.username)
                                .font(Font.custom("PingFang SC", size: 18).weight(.bold))
                                .foregroundColor(.black)
                            
                            Text("用户ID: \(userState.userId)")
                                .font(Font.custom("PingFang SC", size: 14))
                                .foregroundColor(Color.gray)
                                .padding(.top, 6)
                            
                            Spacer().frame(height: 10)
                        }
                        
                        Spacer()

                        // 功能按钮组
                        HStack(spacing: 8) {
                            // 贴纸按钮
                            Button(action: {
                                showStickerView = true
                            }) {
                                HStack(spacing: 4) {
                                    Image(systemName: "face.smiling.fill")
                                        .font(.system(size: 14))
                                    Text("贴纸")
                                        .font(Font.custom("PingFang SC", size: 14).weight(.medium))
                                }
                                .foregroundColor(.white)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(
                                    LinearGradient(
                                        colors: [Color.purple, Color.pink],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                                .cornerRadius(15)
                                .shadow(color: Color.purple.opacity(0.3), radius: 3, x: 0, y: 2)
                            }

                            // 补光按钮
                            Button(action: {
                                showDepthLightingView = true
                            }) {
                                HStack(spacing: 4) {
                                    Image(systemName: "lightbulb.fill")
                                        .font(.system(size: 14))
                                    Text("补光")
                                        .font(Font.custom("PingFang SC", size: 14).weight(.medium))
                                }
                                .foregroundColor(.white)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(
                                    LinearGradient(
                                        colors: [Color.blue, Color.cyan],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                                .cornerRadius(15)
                                .shadow(color: Color.blue.opacity(0.3), radius: 3, x: 0, y: 2)
                            }

                            // 超分按钮
                            Button(action: {
                                showSuperResolutionView = true
                            }) {
                                HStack(spacing: 4) {
                                    Image(systemName: "arrow.up.right.square.fill")
                                        .font(.system(size: 14))
                                    Text("超分")
                                        .font(Font.custom("PingFang SC", size: 14).weight(.medium))
                                }
                                .foregroundColor(.white)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(
                                    LinearGradient(
                                        colors: [Color.orange, Color.yellow],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                                .cornerRadius(15)
                                .shadow(color: Color.orange.opacity(0.3), radius: 3, x: 0, y: 2)
                            }
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 20)
                    
                    // 捏币账户区域
                    VStack(spacing: 12) {
                        HStack {
                            Image("m-1")
                                .resizable()
                                .frame(width: 24, height: 24)
                            
                            Text("捏币账户")
                                .font(Font.custom("PingFang SC", size: 16).weight(.medium))
                                .foregroundColor(.black)
                            
                            Spacer()
                        }
                        
                        HStack {
                            Text("账户余额: \(userState.balance) 捏币")
                                .font(Font.custom("PingFang SC", size: 14))
                                .foregroundColor(.black.opacity(0.8))
                            
                            Spacer()
                            
                            NavigationLink(destination: CoinShopView().navigationBarHidden(true)) {
                                HStack(spacing: 4) {
                                    Image("m-2")
                                        .resizable()
                                        .frame(width: 20, height: 20)
                                    Text("捏币商城")
                                        .font(Font.custom("PingFang SC", size: 14))
                                    Image(systemName: "chevron.right")
                                        .resizable()
                                        .frame(width: 8, height: 14)
                                }
                                .foregroundColor(.black)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(Color.white.opacity(0.7))
                                .cornerRadius(15)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 15)
                                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                                )
                            }
                        }
                    }
                    .padding(16)
                    .cornerRadius(15)
                    .padding(.horizontal, 16)
                    .padding(.top, 20)
                    
                    // 创作记录视图 - 修改为仅占一行高度
                    CreationRecordView()
                        .padding(.bottom, 5)
                    
                    // 作品/喜欢选择区域
                    HStack(spacing: 0) {
                        Button(action: {
                            withAnimation { selectedContentTab = 0 }
                        }) {
                            VStack(spacing: 8) {
                                Text("作品")
                                    .font(Font.custom("PingFang SC", size: 16).weight(.bold))
                                    .foregroundColor(selectedContentTab == 0 ? .black : .gray)
                                
                                if selectedContentTab == 0 {
                                    Capsule()
                                        .frame(height: 3)
                                        .foregroundColor(.blue)
                                        .transition(.opacity)
                                }
                            }
                            .frame(maxWidth: .infinity)
                        }
                        
                        Button(action: {
                            withAnimation { 
                                // 只有当切换到喜欢标签页且需要刷新时才加载
                                if selectedContentTab != 1 {
                                    selectedContentTab = 1 
                                    if needRefreshLikedImages {
                                        loadLikedImages()
                                        needRefreshLikedImages = false
                                    }
                                }
                            }
                        }) {
                            VStack(spacing: 8) {
                                Text("喜欢")
                                    .font(Font.custom("PingFang SC", size: 16).weight(.bold))
                                    .foregroundColor(selectedContentTab == 1 ? .black : .gray)
                                
                                if selectedContentTab == 1 {
                                    Capsule()
                                        .frame(height: 3)
                                        .foregroundColor(.blue)
                                        .transition(.opacity)
                                }
                            }
                            .frame(maxWidth: .infinity)
                        }
                    }
                    .padding(.top, 2)
                    .padding(.horizontal, 40)
                    
                    // 显示加载状态、错误信息或图片列表
                    ZStack {
                        if selectedContentTab == 0 {
                            // 作品标签页
                            if isLoading && userImages.isEmpty {
                                VStack {
                                    Spacer()
                                    ProgressView("加载中...")
                                        .progressViewStyle(CircularProgressViewStyle())
                                        .scaleEffect(1.5)
                                    Spacer()
                                }
                            } else if let error = errorMessage {
                                VStack {
                                    Spacer()
                                    Text(error)
                                        .font(Font.custom("PingFang SC", size: 16))
                                        .foregroundColor(.red)
                                    
                                    Button(action: {
                                        loadUserData()
                                    }) {
                                        Text("重新加载")
                                            .font(Font.custom("PingFang SC", size: 16))
                                            .foregroundColor(.white)
                                            .padding(.vertical, 10)
                                            .padding(.horizontal, 20)
                                            .background(Color.blue)
                                            .cornerRadius(10)
                                    }
                                    .padding(.top, 10)
                                    Spacer()
                                }
                            } else if userImages.isEmpty {
                                VStack {
                                    Spacer()
                                    Text("暂无作品")
                                        .font(Font.custom("PingFang SC", size: 16))
                                        .foregroundColor(.gray)
                                    Spacer()
                                }
                            } else {
                                // 两列独立卡片布局
                                ScrollView {
                                    LazyVGrid(columns: gridLayout, spacing: 16) {
                                        ForEach(userImages) { image in
                                            UserImageCard(image: image, geometry: geometry)
                                                .environmentObject(userState)
                                        }
                                    }
                                    .padding(16)
                                    .padding(.bottom, 100) // 增加底部空间，确保可以滚动到足够低的位置触发加载
                                    
                                    // 加载更多指示器
                                    if hasMoreUserImages {
                                        HStack {
                                            Spacer()
                                            if isLoadingMoreUserImages {
                                                ProgressView("加载更多...")
                                            } else {
                                                Text("上拉加载更多")
                                                    .font(.caption)
                                                    .foregroundColor(.gray)
                                                    .id("userImagesLoadMore") // 添加唯一ID
                                            }
                                            Spacer()
                                        }
                                        .frame(height: 50)
                                        .padding()
                                        .onAppear {
                                            // 避免重复加载
                                            if !isLoadingMoreUserImages {
                                                loadMoreUserImagesWithDebounce()
                                            }
                                        }
                                        // 添加GeometryReader来检测这个视图是否出现在屏幕上
                                        .background(
                                            GeometryReader { loadMoreGeometry -> Color in
                                                // 计算这个视图相对于ScrollView的位置
                                                let offset = loadMoreGeometry.frame(in: .global).minY
                                                
                                                // 当这个视图出现在屏幕底部附近时触发加载
                                                DispatchQueue.main.async {
                                                    if offset < UIScreen.main.bounds.height * 1.2 && !isLoadingMoreUserImages && hasMoreUserImages {
                                                        loadMoreUserImagesWithDebounce()
                                                    }
                                                }
                                                
                                                return Color.clear
                                            }
                                        )
                                    } else if !userImages.isEmpty {
                                        HStack {
                                            Spacer()
                                            Text("没有更多内容了")
                                                .font(.caption)
                                                .foregroundColor(.gray)
                                            Spacer()
                                        }
                                        .frame(height: 50)
                                        .padding()
                                    }
                                }
                                .id(worksViewId) // 强制刷新ID
                                // 下拉刷新：刷新作品列表以更新审核状态
                                .refreshable {
                                    // 使用 withCheckedContinuation 桥接旧的完成回调模式
                                    await withCheckedContinuation { continuation in
                                        loadUserData {
                                            self.worksViewId = UUID() // 更新ID以触发刷新
                                            continuation.resume()
                                        }
                                    }
                                }
                            }
                        } else {
                            // 喜欢标签页
                            if isLoadingLiked && likedImages.isEmpty {
                                VStack {
                                    Spacer()
                                    ProgressView("加载中...")
                                        .progressViewStyle(CircularProgressViewStyle())
                                        .scaleEffect(1.5)
                                    Spacer()
                                }
                            } else if let error = likedErrorMessage {
                                VStack {
                                    Spacer()
                                    Text(error)
                                        .font(Font.custom("PingFang SC", size: 16))
                                        .foregroundColor(.red)
                                    
                                    Button(action: {
                                        loadLikedImages()
                                    }) {
                                        Text("重新加载")
                                            .font(Font.custom("PingFang SC", size: 16))
                                            .foregroundColor(.white)
                                            .padding(.vertical, 10)
                                            .padding(.horizontal, 20)
                                            .background(Color.blue)
                                            .cornerRadius(10)
                                    }
                                    .padding(.top, 10)
                                    Spacer()
                                }
                            } else if likedImages.isEmpty {
                                VStack {
                                    Spacer()
                                    Text("暂无喜欢的作品")
                                        .font(Font.custom("PingFang SC", size: 16))
                                        .foregroundColor(.gray)
                                    Spacer()
                                }
                            } else {
                                // 两列独立卡片布局 - 喜欢的作品
                                ScrollView {
                                    LazyVGrid(columns: gridLayout, spacing: 16) {
                                        ForEach(likedImages) { image in
                                            LikedImageCard(image: image, geometry: geometry)
                                                .environmentObject(userState)
                                        }
                                    }
                                    .padding(16)
                                    .padding(.bottom, 100) // 增加底部空间，确保可以滚动到足够低的位置触发加载
                                    
                                    // 加载更多指示器
                                    if hasMoreLikedImages {
                                        HStack {
                                            Spacer()
                                            if isLoadingMoreLiked {
                                                ProgressView("加载更多...")
                                            } else {
                                                Text("上拉加载更多")
                                                    .font(.caption)
                                                    .foregroundColor(.gray)
                                                    .id("likedImagesLoadMore") // 添加唯一ID
                                            }
                                            Spacer()
                                        }
                                        .frame(height: 50)
                                        .padding()
                                        .onAppear {
                                            // 避免重复加载
                                            if !isLoadingMoreLiked {
                                                loadMoreLikedImagesWithDebounce()
                                            }
                                        }
                                        // 添加GeometryReader来检测这个视图是否出现在屏幕上
                                        .background(
                                            GeometryReader { loadMoreGeometry -> Color in
                                                // 计算这个视图相对于ScrollView的位置
                                                let offset = loadMoreGeometry.frame(in: .global).minY
                                                
                                                // 当这个视图出现在屏幕底部附近时触发加载
                                                DispatchQueue.main.async {
                                                    if offset < UIScreen.main.bounds.height * 1.2 && !isLoadingMoreLiked && hasMoreLikedImages {
                                                        loadMoreLikedImagesWithDebounce()
                                                    }
                                                }
                                                
                                                return Color.clear
                                            }
                                        )
                                    } else if !likedImages.isEmpty {
                                        HStack {
                                            Spacer()
                                            Text("没有更多内容了")
                                                .font(.caption)
                                                .foregroundColor(.gray)
                                            Spacer()
                                        }
                                        .frame(height: 50)
                                        .padding()
                                    }
                                }
                                .id(likedViewId) // 强制刷新ID
                                .refreshable {
                                    // 为喜欢列表也添加下拉刷新
                                    await withCheckedContinuation { continuation in
                                        loadLikedImages {
                                            self.likedViewId = UUID() // 更新ID以触发刷新
                                            continuation.resume()
                                        }
                                    }
                                }
                            }
                        }
                    }
                    .frame(maxHeight: .infinity)
                }
                
                // 2. 底部导航栏 - 通过ContentView提供，这里不需要
                
                // 3. AI创作弹窗
                if userState.showAICreateSheet {
                    CreateSheet(isPresented: $userState.showAICreateSheet)
                }
                
                // 4. 临时提示框
                                    if showToast {
                        VStack {
                            Text("跳转到捏币商城")
                                .font(Font.custom("PingFang SC", size: 16))
                                .foregroundColor(.white)
                                .padding(.horizontal, 20)
                                .padding(.vertical, 10)
                                .background(Color.black.opacity(0.7))
                                .cornerRadius(20)
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .transition(.opacity)
                    }
                
                // 设置菜单
                if showSettingsMenu {
                    VStack(alignment: .trailing) {
                        HStack {
                            Spacer()
                            VStack(alignment: .leading, spacing: 0) {
                                // 添加隐私政策选项
                                Button(action: {
                                    showSettingsMenu = false
                                    // 显示隐私政策网页
                                    userState.showPrivacyPolicy = true
                                }) {
                                    Text("隐私政策")
                                        .font(Font.custom("PingFang SC", size: 16))
                                        .foregroundColor(.black)
                                        .padding(.vertical, 10)
                                        .padding(.horizontal, 16)
                                }
                                
                                Divider()
                                
                                // 添加注销账户选项
                                NavigationLink(destination: DeleteAccountView().environmentObject(userState).navigationBarHidden(true)) {
                                    Text("注销账户")
                                        .font(Font.custom("PingFang SC", size: 16))
                                        .foregroundColor(.red)
                                        .padding(.vertical, 10)
                                        .padding(.horizontal, 16)
                                }
                                .simultaneousGesture(TapGesture().onEnded {
                                    showSettingsMenu = false
                                })
                                
                                Divider()
                                
                                Button(action: {
                                    showLogoutAlert = true
                                    showSettingsMenu = false
                                }) {
                                    Text("退出登录")
                                        .font(Font.custom("PingFang SC", size: 16))
                                        .foregroundColor(.black)
                                        .padding(.vertical, 10)
                                        .padding(.horizontal, 16)
                                }
                            }
                            .background(Color.white)
                            .cornerRadius(8)
                            .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
                            .frame(width: 120)
                            .padding(.top, 60)
                            .padding(.trailing, 20)
                        }
                        Spacer()
                    }
                    .background(
                        Color.black.opacity(0.01)
                            .edgesIgnoringSafeArea(.all)
                            .onTapGesture {
                                withAnimation {
                                    showSettingsMenu = false
                                }
                            }
                    )
                }
                
                // 退出登录确认弹窗
                if showLogoutAlert {
                    Color.black.opacity(0.4)
                        .edgesIgnoringSafeArea(.all)
                        .onTapGesture {} // 防止点击背景关闭弹窗
                    
                    VStack(spacing: 20) {
                        Text("退出登录将会清空创作记录，是否确认退出？")
                            .font(Font.custom("PingFang SC", size: 16))
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, 20)
                        
                        HStack(spacing: 20) {
                            Button(action: {
                                showLogoutAlert = false
                            }) {
                                Text("取消")
                                    .font(Font.custom("PingFang SC", size: 16))
                                    .foregroundColor(.gray)
                                    .frame(width: 100)
                                    .padding(.vertical, 10)
                                    .background(Color.white)
                                    .cornerRadius(20)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 20)
                                            .stroke(Color.gray.opacity(0.5), lineWidth: 1)
                                    )
                            }
                            
                            Button(action: {
                                handleLogout()
                            }) {
                                Text("确定")
                                    .font(Font.custom("PingFang SC", size: 16))
                                    .foregroundColor(.white)
                                    .frame(width: 100)
                                    .padding(.vertical, 10)
                                    .background(Color.red)
                                    .cornerRadius(20)
                            }
                        }
                    }
                    .padding(24)
                    .background(Color.white)
                    .cornerRadius(16)
                    .shadow(color: Color.black.opacity(0.2), radius: 10, x: 0, y: 5)
                    .padding(.horizontal, 40)
                }
                
                // 导航到SelectView
                NavigationLink(destination: SelectView().navigationBarHidden(true), isActive: $navigateToSelectView) {
                    EmptyView()
                }
                
                // 导航到超分页面
                .fullScreenCover(isPresented: $showSuperResolutionView) {
                    SuperResolutionView()
                        .environmentObject(userState)
                }

                // 导航到补光页面
                .fullScreenCover(isPresented: $showDepthLightingView) {
                    DepthLightingView()
                        .environmentObject(userState)
                }

                // 导航到贴纸页面
                .fullScreenCover(isPresented: $showStickerView) {
                    StickerCreationView()
                        .environmentObject(userState)
                }
                
                // 添加隐私政策弹窗
                .sheet(isPresented: $userState.showPrivacyPolicy) {
                    WebViewContainer(urlString: "https://qianmianshixiao.com:5190/privacy")
                }
                
            }
            .ignoresSafeArea(.all, edges: [.top, .horizontal])
            .onAppear {
                UserState.shared.updatePageStatus(page: "ProfileView", conversationId: "")
                if needRefreshUserImages {
                    loadUserData()
                    needRefreshUserImages = false
                }

                // 只有当当前是喜欢标签页且需要刷新时才加载
                if selectedContentTab == 1 && needRefreshLikedImages {
                    loadLikedImages()
                    needRefreshLikedImages = false
                }
                
                NotificationCenter.default.addObserver(
                    forName: NSNotification.Name("RefreshUserImages"),
                    object: nil,
                    queue: .main
                ) { _ in
                    // 标记需要刷新，但不立即刷新，等到下次显示时再刷新
                    needRefreshUserImages = true
                }
                
                // 添加喜欢图片变化的通知观察者
                NotificationCenter.default.addObserver(
                    forName: NSNotification.Name("RefreshHomeImages"),
                    object: nil,
                    queue: .main
                ) { notification in
                    if let userInfo = notification.userInfo,
                       let imageId = userInfo["imageId"] as? Int,
                       let liked = userInfo["liked"] as? Bool,
                       let likeCount = userInfo["likeCount"] as? Int {
                        
                        // 处理自己作品的点赞状态变化
                        for (index, image) in self.userImages.enumerated() {
                            if image.imageId == imageId {
                                // 创建更新后的图片数据
                                let updatedImage = ImageData(
                                    imageId: image.imageId,
                                    userId: image.userId,
                                    username: image.username,
                                    avatar: image.avatar,
                                    storagePath: image.storagePath,
                                    likeCount: likeCount,
                                    createdAt: image.createdAt,
                                    liked: liked
                                )
                                
                                // 更新数组中的图片
                                self.userImages[index] = updatedImage
                                break
                            }
                        }
                        
                        // 如果状态为已点赞，确保喜欢列表中包含该图片
                        if liked {
                            // 检查是否已在喜欢列表中
                            let exists = self.likedImages.contains(where: { $0.imageId == imageId })
                            // 如果不在喜欢列表中，标记需要刷新
                            if !exists {
                                needRefreshLikedImages = true
                                // 如果当前正在显示喜欢栏，立即刷新
                                if selectedContentTab == 1 && !isLoadingLiked && !isLoadingMoreLiked {
                                    loadLikedImages()
                                    needRefreshLikedImages = false
                                }
                            }
                        } else {
                            // 取消点赞，从喜欢列表中移除
                            if self.likedImages.contains(where: { $0.imageId == imageId }) {
                                self.likedImages.removeAll { $0.imageId == imageId }
                                // 如果当前显示的是喜欢栏且列表为空，只有此时才重新加载
                                if selectedContentTab == 1 && likedImages.isEmpty && !isLoadingLiked && !isLoadingMoreLiked {
                                    loadLikedImages()
                                    needRefreshLikedImages = false
                                }
                            }
                        }
                    } else {
                        // 如果没有特定图片信息，标记需要刷新但不立即加载
                        needRefreshLikedImages = true
                    }
                }
                
                NotificationCenter.default.addObserver(
                    forName: NSNotification.Name("ImageDeleted"),
                    object: nil,
                    queue: .main
                ) { notification in
                    if let userInfo = notification.userInfo,
                       let imageId = userInfo["imageId"] as? Int {
                        
                        // 直接从当前列表中移除图片，无需重新加载
                        self.userImages.removeAll { $0.imageId == imageId }
                        
                        // 如果被删除的图片也在喜欢列表中，标记需要刷新喜欢列表但不立即加载
                        if self.likedImages.contains(where: { $0.imageId == imageId }) {
                            self.likedImages.removeAll { $0.imageId == imageId }
                            needRefreshLikedImages = true
                        }
                    }
                }
                
                // 添加监听删除喜欢图片的通知观察者
                NotificationCenter.default.addObserver(
                    forName: NSNotification.Name("RemoveLikedImage"),
                    object: nil,
                    queue: .main
                ) { notification in
                    if let userInfo = notification.userInfo,
                       let imageId = userInfo["imageId"] as? Int {
                        // 立即从喜欢列表中移除
                        self.likedImages.removeAll { $0.imageId == imageId }
                    }
                }
            }
            .onDisappear {
            

                NotificationCenter.default.removeObserver(self)
            }
        }
        
    }
    
    
    private func loadUserData(resetImages: Bool = true, completion: (() -> Void)? = nil) {
        if resetImages {
            isLoading = true
            errorMessage = nil
            userImagesSkip = 0
        } else {
            isLoadingMoreUserImages = true
        }
        
        // 不再每次都从服务器获取余额，使用本地保存的余额
        
        // 加载用户上传的图片
        APIService.shared.getUserImages(userId: userState.userId, skip: userImagesSkip) { result in
            DispatchQueue.main.async {
                if resetImages {
                    isLoading = false
                } else {
                    isLoadingMoreUserImages = false
                }
                
                switch result {
                case .success(let images):
                    if resetImages {
                        self.userImages = images
                    } else {
                        // 添加新图片并去重
                        let existingIds = Set(self.userImages.map { $0.imageId })
                        let newImages = images.filter { !existingIds.contains($0.imageId) }
                        self.userImages.append(contentsOf: newImages)
                    }
                    
                    // 更新跳过的数量
                    self.userImagesSkip += images.count
                    
                    // 检查是否还有更多图片 - 如果获取到的图片数量小于页面大小，则没有更多图片
                    self.hasMoreUserImages = images.count >= 20 // 假设每页20张图片
                    
                case .failure(let error):
                    self.errorMessage = "加载图片失败: \(error.localizedDescription)"
                }
                
                // 异步操作完成后调用回调
                completion?()
            }
        }
    }
    
    private func loadLikedImages(resetImages: Bool = true, completion: (() -> Void)? = nil) {
        if resetImages {
            isLoadingLiked = true
            likedErrorMessage = nil
            likedImagesSkip = 0
        } else {
            isLoadingMoreLiked = true
        }
        
        // 加载用户喜欢的图片
        APIService.shared.getUserLikedImages(userId: userState.userId, skip: likedImagesSkip) { result in
            DispatchQueue.main.async {
                if resetImages {
                    isLoadingLiked = false
                } else {
                    isLoadingMoreLiked = false
                }
                
                switch result {
                case .success(let images):
                    if resetImages {
                        self.likedImages = images
                    } else {
                        // 添加新图片并去重
                        let existingIds = Set(self.likedImages.map { $0.imageId })
                        let newImages = images.filter { !existingIds.contains($0.imageId) }
                        self.likedImages.append(contentsOf: newImages)
                    }
                    
                    // 更新跳过的数量
                    self.likedImagesSkip += images.count
                    
                    // 检查是否还有更多图片 - 如果获取到的图片数量小于页面大小，则没有更多图片
                    self.hasMoreLikedImages = images.count >= 20 // 假设每页20张图片
                    
                case .failure(let error):
                    self.likedErrorMessage = "加载喜欢的图片失败: \(error.localizedDescription)"
                }
                
                // 异步操作完成后调用回调
                completion?()
            }
        }
    }
    
    // 添加加载更多喜欢的图片函数（带防抖机制）
    private func loadMoreLikedImagesWithDebounce() {
        let now = Date()
        // 如果距离上次加载时间不足2秒，则不重复加载
        if now.timeIntervalSince(lastLoadMoreLikedImagesTime) < 2.0 {
            return
        }
        
        lastLoadMoreLikedImagesTime = now
        loadMoreLikedImages()
    }
    
    // 添加加载更多用户图片函数（带防抖机制）
    private func loadMoreUserImagesWithDebounce() {
        let now = Date()
        // 如果距离上次加载时间不足2秒，则不重复加载
        if now.timeIntervalSince(lastLoadMoreUserImagesTime) < 2.0 {
            return
        }
        
        lastLoadMoreUserImagesTime = now
        loadMoreUserImages()
    }
    
    // 添加加载更多喜欢的图片函数
    private func loadMoreLikedImages() {
        guard hasMoreLikedImages && !isLoadingMoreLiked else { return }
        loadLikedImages(resetImages: false)
    }
    
    // 添加加载更多用户图片函数
    private func loadMoreUserImages() {
        guard hasMoreUserImages && !isLoadingMoreUserImages else { return }
        loadUserData(resetImages: false)
    }
}

// 用户图片卡片
struct UserImageCard: View {
    let image: ImageData
    let geometry: GeometryProxy
    @EnvironmentObject var userState: UserState
    @State private var showDeleteButton = false
    @State private var isDeleting = false
    @State private var navigateToDetail = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 5) {
            // 图片
            ZStack {
                // 图片
                AsyncImage(url: APIService.shared.getImageURL(filename: URL(string: image.storagePath)?.lastPathComponent ?? image.storagePath, lowResolution: true)) { phase in
                    switch phase {
                    case .empty:
                        Rectangle()
                            .foregroundColor(.gray.opacity(0.3))
                            .aspectRatio(1.0, contentMode: .fill)
                            .overlay(
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                            )
                    case .success(let image):
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    case .failure:
                        Rectangle()
                            .foregroundColor(.gray.opacity(0.3))
                            .overlay(
                                Image(systemName: "exclamationmark.triangle")
                                    .foregroundColor(.gray)
                            )
                    @unknown default:
                        Rectangle()
                            .foregroundColor(.gray.opacity(0.3))
                    }
                }
                .frame(width: (geometry.size.width - 48) / 2, height: (geometry.size.width - 48) / 2)
                .clipped()
                .cornerRadius(10)
                
                // 审核状态标签
                if let auditStatus = image.auditStatus {
                    VStack {
                        HStack {
                            Spacer()
                            
                            // 根据审核状态显示不同的标签
                            switch auditStatus {
                            case 0: // 审核中
                                Text("审核中")
                                    .font(Font.custom("PingFang SC", size: 10))
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 3)
                                    .background(Color.orange.opacity(0.8))
                                    .cornerRadius(4)
                            case 2: // 审核不通过
                                Text("未通过")
                                    .font(Font.custom("PingFang SC", size: 10))
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 3)
                                    .background(Color.red.opacity(0.8))
                                    .cornerRadius(4)
                            default: // 审核通过或其他状态不显示标签
                                EmptyView()
                            }
                        }
                        .padding(6)
                        
                        Spacer()
                    }
                }
                
                // 处理点击导航到详情页
                Rectangle()
                    .fill(Color.clear)
                    .frame(width: (geometry.size.width - 48) / 2, height: (geometry.size.width - 48) / 2)
                    .contentShape(Rectangle())
                    .onTapGesture {
                        navigateToDetail = true
                    }
                
                // 导航链接
                .fullScreenCover(isPresented: $navigateToDetail) {
                    ImageDetailView(imageId: image.imageId)
                        .environmentObject(userState)
                }
                
                // 加载指示器
                if isDeleting {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle())
                        .scaleEffect(1.5)
                }
            }
            
            // 日期信息和删除按钮放在同一行
            HStack {
                // 删除图标按钮 - 放在左侧
                Button(action: {
                    deleteImage()
                }) {
                    Image("rb")
                        .resizable()
                        .frame(width: 18, height: 18)
                        .padding(8) // 增加内边距扩大点击区域
                        .contentShape(Rectangle()) // 确保整个区域可点击
                }
                .disabled(isDeleting)
                .buttonStyle(BorderlessButtonStyle()) // 防止父视图拦截点击事件
                
                Spacer()
                
                Text(formatDate(image.createdAt))
                    .font(Font.custom("PingFang SC", size: 10))
                    .foregroundColor(.gray)
            }
            .padding(.horizontal, 5)
            .padding(.vertical, 3)
        }
        .frame(width: (geometry.size.width - 48) / 2)
        .background(Color.white.opacity(0.7))
        .cornerRadius(10)
        .shadow(color: Color.black.opacity(0.1), radius: 3, x: 0, y: 1)
        .id("userImage-\(image.imageId)") // 添加唯一ID防止删除时的布局抖动
    }
    
    private func deleteImage() {
        isDeleting = true
        
        APIService.shared.deleteImage(imageId: image.imageId, userId: userState.userId) { result in
            DispatchQueue.main.async {
                isDeleting = false
                showDeleteButton = false
                
                switch result {
                case .success:
                    // 发送通知，包含被删除的图片ID
                    NotificationCenter.default.post(
                        name: NSNotification.Name("ImageDeleted"),
                        object: nil,
                        userInfo: ["imageId": image.imageId]
                    )
                    
                    // 不再发送刷新用户图片列表通知，避免不必要的API调用
                    // NotificationCenter.default.post(name: NSNotification.Name("RefreshUserImages"), object: nil)
                case .failure(let error):
                    print("删除图片失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    // 格式化日期
    private func formatDate(_ dateString: String) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
        
        guard let date = dateFormatter.date(from: dateString) else {
            return dateString
        }
        
        let outputFormatter = DateFormatter()
        outputFormatter.dateFormat = "MM-dd"
        return outputFormatter.string(from: date)
    }
}

// 喜欢的图片卡片
struct LikedImageCard: View {
    let image: ImageData
    let geometry: GeometryProxy
    @EnvironmentObject var userState: UserState
    @State private var isLiked: Bool
    @State private var likeCount: Int
    @State private var navigateToDetail = false
    
    init(image: ImageData, geometry: GeometryProxy) {
        self.image = image
        self.geometry = geometry
        self._likeCount = State(initialValue: image.likeCount)
        self._isLiked = State(initialValue: image.liked)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 5) {
            // 图片
            ZStack {
                AsyncImage(url: APIService.shared.getImageURL(filename: URL(string: image.storagePath)?.lastPathComponent ?? image.storagePath, lowResolution: true)) { phase in
                    switch phase {
                    case .empty:
                        Rectangle()
                            .foregroundColor(.gray.opacity(0.3))
                            .aspectRatio(1.0, contentMode: .fit)
                            .overlay(
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                            )
                    case .success(let image):
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .clipped()
                    case .failure:
                        Rectangle()
                            .foregroundColor(.gray.opacity(0.3))
                            .aspectRatio(1.0, contentMode: .fit)
                            .overlay(
                                Image(systemName: "exclamationmark.triangle")
                                    .foregroundColor(.gray)
                            )
                    @unknown default:
                        Rectangle()
                            .foregroundColor(.gray.opacity(0.3))
                    }
                }
                .frame(width: (geometry.size.width - 48) / 2, height: (geometry.size.width - 48) / 2)
                .cornerRadius(10)
                
                // 处理点击导航到详情页
                Rectangle()
                    .fill(Color.clear)
                    .frame(width: (geometry.size.width - 48) / 2, height: (geometry.size.width - 48) / 2)
                    .contentShape(Rectangle())
                    .onTapGesture {
                        navigateToDetail = true
                    }
                
                .fullScreenCover(isPresented: $navigateToDetail) {
                    ImageDetailView(imageId: image.imageId)
                        .environmentObject(userState)
                }
            }
            
            // 用户信息
            HStack {
                // 头像
                Image(image.avatar)
                    .resizable()
                    .scaledToFill()
                    .frame(width: 20, height: 20)
                    .clipShape(Circle())
                
                // 用户名
                Text(image.username)
                    .font(Font.custom("PingFang SC", size: 12))
                    .foregroundColor(.black)
                    .lineLimit(1)
                
                Spacer()
                
                // 点赞按钮
                Button(action: {
                    toggleLike()
                }) {
                    Image(isLiked ? "5-5" : "5-4")
                        .resizable()
                        .frame(width: 16, height: 16)
                        .padding(8) // 增加内边距扩大点击区域
                        .contentShape(Rectangle()) // 确保整个区域可点击
                }
                .buttonStyle(BorderlessButtonStyle()) // 防止父视图拦截点击事件
                
                Text("\(likeCount)")
                    .font(Font.custom("PingFang SC", size: 12))
                    .foregroundColor(.gray)
            }
            .padding(.horizontal, 5)
        }
        .frame(width: (geometry.size.width - 48) / 2)
        .padding(.bottom, 5)
        .background(Color.white.opacity(0.7))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
        .id("likedImage-\(image.imageId)") // 添加唯一ID防止布局抖动
    }
    
    private func toggleLike() {
        APIService.shared.likeImage(imageId: image.imageId, userId: userState.userId) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let (newLikeCount, liked)):
                    self.isLiked = liked
                    self.likeCount = newLikeCount
                    
                    // 发送通知更新图片状态
                    NotificationCenter.default.post(
                        name: NSNotification.Name("RefreshHomeImages"),
                        object: nil,
                        userInfo: ["imageId": image.imageId, "liked": liked, "likeCount": newLikeCount]
                    )
                    
                    // 如果取消点赞，发送通知立即从列表中移除此卡片
                    if !liked {
                        NotificationCenter.default.post(
                            name: NSNotification.Name("RemoveLikedImage"),
                            object: nil,
                            userInfo: ["imageId": image.imageId]
                        )
                    }
                case .failure(let error):
                    print("个人主页点赞操作失败: \(error.localizedDescription)")
                }
            }
        }
    }
}

// 样例数据，以备预览使用
struct Work: Identifiable {
    let id = UUID()
    let imageName: String
    let likes: Int
}

func sampleWorks() -> [Work] {
    return [
        Work(imageName: "4-1", likes: 256),
        Work(imageName: "4-2", likes: 128),
        Work(imageName: "4-3", likes: 64),
        Work(imageName: "4-4", likes: 32)
    ]
}

struct ProfileView_Previews: PreviewProvider {
    static var previews: some View {
        ProfileView()
            .environmentObject(UserState())
    }
}


