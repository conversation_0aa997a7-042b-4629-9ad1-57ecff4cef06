import SwiftUI
import Vision
import CoreImage
import CoreImage.CIFilterBuiltins
import PhotosUI

struct StickerCreationView: View {
    @EnvironmentObject var userState: UserState
    @Environment(\.presentationMode) var presentationMode
    
    @State private var selectedImage: UIImage?
    @State private var processedImage: UIImage?
    @State private var segmentationResultImage: UIImage? // 新增：显示分割结果
    @State private var showImagePicker = false
    @State private var isProcessing = false
    @State private var showBackground = true
    @State private var errorMessage: String?
    @State private var showAlert = false
    @State private var alertMessage = ""
    
    // 图片选择器相关
    @State private var selectedPhotoItem: PhotosPickerItem?
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.white.ignoresSafeArea() // 改为白色背景
                
                VStack(spacing: 20) {
                    // 顶部导航栏
                    HStack {
                        Button("取消") {
                            presentationMode.wrappedValue.dismiss()
                        }
                        .foregroundColor(.black) // 改为黑色文字

                        Spacer()

                        Text("贴纸创作")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.black) // 改为黑色文字

                        Spacer()

                        Button("保存") {
                            saveImage()
                        }
                        .foregroundColor(.black) // 改为黑色文字
                        .disabled(processedImage == nil)
                    }
                    .padding(.horizontal)
                    .padding(.top, 10)
                    
                    // 主要内容区域
                    VStack(spacing: 20) {
                        // 图片显示区域
                        ZStack {
                            RoundedRectangle(cornerRadius: 15)
                                .fill(Color.gray.opacity(0.2))
                                .frame(height: 400)
                            
                            if let image = processedImage {
                                Image(uiImage: image)
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                                    .frame(maxHeight: 380)
                                    .cornerRadius(15)
                            } else if let image = selectedImage {
                                Image(uiImage: image)
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                                    .frame(maxHeight: 380)
                                    .cornerRadius(15)
                            } else {
                                VStack(spacing: 15) {
                                    Image(systemName: "photo.badge.plus")
                                        .font(.system(size: 50))
                                        .foregroundColor(.gray)
                                    
                                    Text("点击上传图片")
                                        .font(.headline)
                                        .foregroundColor(.gray)
                                }
                            }
                            
                            // 处理中的加载指示器
                            if isProcessing {
                                VStack(spacing: 10) {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        .scaleEffect(1.5)
                                    
                                    Text("正在处理...")
                                        .foregroundColor(.black) // 改为黑色文字
                                        .font(.caption)
                                }
                                .padding()
                                .background(Color.black.opacity(0.7))
                                .cornerRadius(10)
                            }
                        }
                        .onTapGesture {
                            if !isProcessing {
                                showImagePicker = true
                            }
                        }
                        
                        // 控制按钮
                        if processedImage != nil {
                            HStack(spacing: 20) {
                                Button(action: {
                                    showBackground.toggle()
                                    updateProcessedImage()
                                }) {
                                    HStack(spacing: 8) {
                                        Image(systemName: showBackground ? "eye.fill" : "eye.slash.fill")
                                        Text(showBackground ? "隐藏背景" : "显示背景")
                                    }
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 20)
                                    .padding(.vertical, 10)
                                    .background(Color.blue)
                                    .cornerRadius(20)
                                }
                                
                                Button(action: {
                                    selectedImage = nil
                                    processedImage = nil
                                    segmentationResultImage = nil // 清除分割结果
                                    showBackground = true
                                    showImagePicker = true
                                }) {
                                    HStack(spacing: 8) {
                                        Image(systemName: "photo.badge.plus")
                                        Text("重新选择")
                                    }
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 20)
                                    .padding(.vertical, 10)
                                    .background(Color.gray)
                                    .cornerRadius(20)
                                }
                            }

                            // 分割结果显示区域
                            if let segmentationResult = segmentationResultImage {
                                VStack(spacing: 10) {
                                    Text("分割结果预览")
                                        .font(.headline)
                                        .foregroundColor(.black)

                                    Image(uiImage: segmentationResult)
                                        .resizable()
                                        .aspectRatio(contentMode: .fit)
                                        .frame(maxHeight: 150)
                                        .cornerRadius(10)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 10)
                                                .stroke(Color.gray.opacity(0.5), lineWidth: 1)
                                        )

                                    Text("白色区域为检测到的人物")
                                        .font(.caption)
                                        .foregroundColor(.gray)
                                }
                                .padding(.top, 20)
                            }
                        }

                        Spacer()
                    }
                    .padding(.horizontal)
                }
            }
        }
        .navigationBarHidden(true)
        .photosPicker(isPresented: $showImagePicker, selection: $selectedPhotoItem, matching: .images)
        .onChange(of: selectedPhotoItem) { _, newItem in
            Task {
                if let data = try? await newItem?.loadTransferable(type: Data.self),
                   let image = UIImage(data: data) {
                    await MainActor.run {
                        self.selectedImage = image
                        self.processImage(image)
                    }
                }
            }
        }
        .alert(isPresented: $showAlert) {
            Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
        }
    }
    
    private func processImage(_ image: UIImage) {
        isProcessing = true
        errorMessage = nil
        
        performPersonSegmentation(image: image) { segmentationMask in
            DispatchQueue.main.async {
                self.isProcessing = false
                
                if let mask = segmentationMask {
                    self.createStickerEffect(originalImage: image, segmentationMask: mask)
                } else {
                    self.errorMessage = "无法检测到人物，请选择包含人物的图片"
                    self.showAlert(message: "无法检测到人物，请选择包含人物的图片")
                }
            }
        }
    }
    
    private func performPersonSegmentation(image: UIImage, completion: @escaping (CVPixelBuffer?) -> Void) {
        DispatchQueue.global(qos: .userInitiated).async {
            let segmentationRequest = VNGenerateForegroundInstanceMaskRequest { request, error in
                if let error = error {
                    print("❌ [DEBUG] 前景分割错误: \(error.localizedDescription)")
                    completion(nil)
                    return
                }

                guard let observations = request.results as? [VNInstanceMaskObservation],
                      let observation = observations.first else {
                    print("❌ [DEBUG] 未检测到前景对象")
                    completion(nil)
                    return
                }

                // 直接返回instanceMask的CVPixelBuffer
                completion(observation.instanceMask)
            }

            // VNGenerateForegroundInstanceMaskRequest 默认使用高质量分割
            
            guard let cgImage = image.cgImage else {
                completion(nil)
                return
            }
            
            let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
            
            do {
                try handler.perform([segmentationRequest])
            } catch {
                print("❌ [DEBUG] 处理图像时出错: \(error.localizedDescription)")
                completion(nil)
            }
        }
    }
    
    private func createStickerEffect(originalImage: UIImage, segmentationMask: CVPixelBuffer) {
        guard let cgImage = originalImage.cgImage else { return }

        let ciImage = CIImage(cgImage: cgImage)
        let maskImage = CIImage(cvPixelBuffer: segmentationMask)

        // 调整mask尺寸以匹配原图
        let scaledMask = maskImage.transformed(by: CGAffineTransform(scaleX: ciImage.extent.width / maskImage.extent.width,
                                                                     y: ciImage.extent.height / maskImage.extent.height))

        // 生成分割结果图像（白色人物，黑色背景）
        // VNGenerateForegroundInstanceMaskRequest返回的是实例掩码，需要转换为二值掩码
        print("🔍 [DEBUG] 原始掩码范围: \(scaledMask.extent)")

        // 直接使用颜色矩阵将非零值转换为1，零值保持0
        // 对于实例掩码，任何大于0的值都表示前景
        let binaryMask = scaledMask.applyingFilter("CIColorMatrix", parameters: [
            "inputRVector": CIVector(x: 1000, y: 1000, z: 1000, w: 0), // 放大非零值
            "inputGVector": CIVector(x: 1000, y: 1000, z: 1000, w: 0),
            "inputBVector": CIVector(x: 1000, y: 1000, z: 1000, w: 0),
            "inputAVector": CIVector(x: 0, y: 0, z: 0, w: 1)
        ]).applyingFilter("CIColorClamp", parameters: [
            "inputMinComponents": CIVector(x: 0, y: 0, z: 0, w: 0),
            "inputMaxComponents": CIVector(x: 1, y: 1, z: 1, w: 1)
        ])

        print("🔍 [DEBUG] 二值掩码创建成功")

        // 为显示创建白色版本的掩码（0-255范围）
        let displayMask = binaryMask.applyingFilter("CIColorMatrix", parameters: [
            "inputRVector": CIVector(x: 255, y: 255, z: 255, w: 0),
            "inputGVector": CIVector(x: 255, y: 255, z: 255, w: 0),
            "inputBVector": CIVector(x: 255, y: 255, z: 255, w: 0),
            "inputAVector": CIVector(x: 0, y: 0, z: 0, w: 1)
        ])

        let context = CIContext()
        if let segmentationCGImage = context.createCGImage(displayMask, from: displayMask.extent) {
            self.segmentationResultImage = UIImage(cgImage: segmentationCGImage)
            print("🔍 [DEBUG] 分割结果图像创建成功")
        } else {
            print("❌ [DEBUG] 无法创建分割结果图像")
        }

        // 创建贴纸效果 - 使用二值掩码（0-1范围）
        let stickerImage = applyStickerEffect(to: ciImage, with: binaryMask)

        // 转换为UIImage
        if let outputCGImage = context.createCGImage(stickerImage, from: stickerImage.extent) {
            self.processedImage = UIImage(cgImage: outputCGImage)
        }
    }
    
    private func applyStickerEffect(to image: CIImage, with mask: CIImage) -> CIImage {
        print("🔍 [DEBUG] applyStickerEffect - 图像尺寸: \(image.extent)")
        print("🔍 [DEBUG] applyStickerEffect - 掩码尺寸: \(mask.extent)")
        print("🔍 [DEBUG] applyStickerEffect - 显示背景: \(showBackground)")

        // 创建人物轮廓（用于测试掩码是否有效）
        let personTest = image.applyingFilter("CIBlendWithMask", parameters: [
            kCIInputMaskImageKey: mask
        ])
        print("🔍 [DEBUG] 人物轮廓创建: \(personTest.extent)")

        // 创建贴纸边缘效果
        let stickerBorder = createStickerBorder(for: image, mask: mask)

        if showBackground {
            // 显示背景：原图 + 贴纸边缘
            print("🔍 [DEBUG] 返回：背景 + 贴纸边缘")
            return stickerBorder.composited(over: image)
        } else {
            // 隐藏背景：只显示人物 + 贴纸边缘，背景透明
            // 首先创建只有人物的图像（无背景）
            let personOnly = image.applyingFilter("CIBlendWithMask", parameters: [
                kCIInputMaskImageKey: mask
            ])
            print("🔍 [DEBUG] 人物图像创建: \(personOnly.extent)")

            // 然后将贴纸边缘合成到人物图像上
            let result = stickerBorder.composited(over: personOnly)
            print("🔍 [DEBUG] 返回：人物 + 贴纸边缘")
            return result
        }
    }
    
    private func createStickerBorder(for originalImage: CIImage, mask: CIImage) -> CIImage {
        // 1. 创建人物轮廓
        let personImage = originalImage.applyingFilter("CIBlendWithMask", parameters: [
            kCIInputMaskImageKey: mask
        ])

        // 2. 创建膨胀的mask用于边缘
        let morphologyFilter = CIFilter(name: "CIMorphologyMaximum")!
        morphologyFilter.setValue(mask, forKey: kCIInputImageKey)
        morphologyFilter.setValue(6, forKey: kCIInputRadiusKey) // 减小边缘宽度

        guard let dilatedMask = morphologyFilter.outputImage else { return personImage }

        // 3. 创建边缘mask（膨胀的mask - 原始mask）
        let edgeMask = dilatedMask.applyingFilter("CIDifferenceBlendMode", parameters: [
            kCIInputBackgroundImageKey: mask
        ])

        // 4. 创建白色胶带边缘
        let whiteColor = CIFilter(name: "CIConstantColorGenerator")!
        whiteColor.setValue(CIColor(red: 0.98, green: 0.98, blue: 0.98, alpha: 1.0), forKey: kCIInputColorKey)

        guard let whiteBorder = whiteColor.outputImage?.cropped(to: originalImage.extent) else { return personImage }

        // 5. 应用边缘mask到白色边框
        let tapeEdge = whiteBorder.applyingFilter("CIBlendWithMask", parameters: [
            kCIInputMaskImageKey: edgeMask
        ])

        // 6. 创建简化的撕纸效果
        let tearEffect = createSimpleTearEffect(for: tapeEdge, edgeMask: edgeMask)

        // 7. 组合人物和贴纸边缘
        return tearEffect.composited(over: personImage)
    }

    private func createSimpleTearEffect(for tapeEdge: CIImage, edgeMask: CIImage) -> CIImage {
        // 创建轻微的噪声纹理，只应用在边缘
        let noiseFilter = CIFilter(name: "CIRandomGenerator")!
        guard let noise = noiseFilter.outputImage else { return tapeEdge }

        let croppedNoise = noise.cropped(to: tapeEdge.extent)

        // 大幅减少噪声强度，只用于边缘纹理
        let subtleNoise = croppedNoise.applyingFilter("CIColorControls", parameters: [
            kCIInputContrastKey: 0.3,
            kCIInputBrightnessKey: 0.7,
            kCIInputSaturationKey: 0.0
        ])

        // 只在边缘区域应用轻微的纹理效果
        let texturedEdge = subtleNoise.applyingFilter("CIBlendWithMask", parameters: [
            kCIInputMaskImageKey: edgeMask
        ])

        // 将纹理与胶带边缘混合，使用更温和的混合模式
        let finalEdge = tapeEdge.applyingFilter("CIOverlayBlendMode", parameters: [
            kCIInputBackgroundImageKey: texturedEdge
        ])

        // 添加轻微的阴影效果
        let shadowOffset = CGAffineTransform(translationX: 1, y: 1)
        let shadowImage = edgeMask.transformed(by: shadowOffset)
            .applyingFilter("CIGaussianBlur", parameters: [kCIInputRadiusKey: 2.0])
            .applyingFilter("CIColorMatrix", parameters: [
                "inputRVector": CIVector(x: 0, y: 0, z: 0, w: 0),
                "inputGVector": CIVector(x: 0, y: 0, z: 0, w: 0),
                "inputBVector": CIVector(x: 0, y: 0, z: 0, w: 0),
                "inputAVector": CIVector(x: 0, y: 0, z: 0, w: 0.2),
                "inputBiasVector": CIVector(x: 0, y: 0, z: 0, w: 0)
            ])

        return finalEdge.composited(over: shadowImage)
    }

    private func createTransparentBackground(size: CGSize) -> CIImage {
        let transparentColor = CIFilter(name: "CIConstantColorGenerator")!
        transparentColor.setValue(CIColor.clear, forKey: kCIInputColorKey)

        return transparentColor.outputImage!.cropped(to: CGRect(origin: .zero, size: size))
    }
    
    private func updateProcessedImage() {
        guard let originalImage = selectedImage else { return }

        // 重新应用效果
        performPersonSegmentation(image: originalImage) { segmentationMask in
            DispatchQueue.main.async {
                if let mask = segmentationMask {
                    self.createStickerEffect(originalImage: originalImage, segmentationMask: mask)
                }
            }
        }
    }
    
    private func saveImage() {
        guard let imageToSave = showBackground ? processedImage : getImageWithoutBackground() else {
            showAlert(message: "没有可保存的图片")
            return
        }
        
        UIImageWriteToSavedPhotosAlbum(imageToSave, nil, nil, nil)
        showAlert(message: "图片已保存到相册")
    }
    
    private func getImageWithoutBackground() -> UIImage? {
        guard let originalImage = selectedImage else { return nil }

        _ = CIImage(cgImage: originalImage.cgImage!)

        // 这里应该返回只有人物和贴纸效果的图片，背景透明
        // 由于当前的实现，我们返回processedImage
        return processedImage
    }
    
    private func showAlert(message: String) {
        alertMessage = message
        showAlert = true
    }
}
