from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, func, Text, TIMESTAMP, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()

class UserAccount(Base):
    __tablename__ = "user_account"

    user_id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(50), unique=True, nullable=False)
    phone_number = Column(String(15), unique=True, nullable=False)
    avatar = Column(String(255), nullable=True)
    balance = Column(Integer, default=0)
    token = Column(String(255), nullable=True)
    validity_period = Column(DateTime, nullable=True)

    # 关系
    images = relationship("ImageData", back_populates="user", cascade="all, delete-orphan")
    liked_images = relationship("UserImageLike", back_populates="user", cascade="all, delete-orphan")
    comments = relationship("Comment", back_populates="user", cascade="all, delete-orphan")
    # 添加私信关系
    sent_messages = relationship("Message", foreign_keys="Message.sender_id", back_populates="sender", cascade="all, delete-orphan")
    received_messages = relationship("Message", foreign_keys="Message.receiver_id", back_populates="receiver", cascade="all, delete-orphan")
    # 会话关系
    conversations1 = relationship("Conversation", foreign_keys="Conversation.user1_id", back_populates="user1", cascade="all, delete-orphan")
    conversations2 = relationship("Conversation", foreign_keys="Conversation.user2_id", back_populates="user2", cascade="all, delete-orphan")
    # 设备令牌关系
    device_tokens = relationship("DeviceToken", back_populates="user", cascade="all, delete-orphan")

    def to_dict(self):
        return {
            "user_id": self.user_id,
            "username": self.username,
            "phone_number": self.phone_number,
            "avatar": self.avatar,
            "balance": self.balance
        }


class ImageData(Base):
    __tablename__ = "image_data"

    image_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("user_account.user_id"), nullable=False)
    audit_status = Column(Integer, default=0, nullable=False, comment='审核状态：0-审核中，1-通过，2-不通过')
    storage_path = Column(String(255), nullable=False)
    like_count = Column(Integer, default=0)
    created_at = Column(DateTime, default=func.now())

    # 关系
    user = relationship("UserAccount", back_populates="images")
    # 添加点赞关系
    likes = relationship("UserImageLike", back_populates="image", cascade="all, delete-orphan")
    # 添加评论关系
    comments = relationship("Comment", back_populates="image", cascade="all, delete-orphan")
    # 添加信息关系
    info = relationship("ImageInfo", back_populates="image", uselist=False, cascade="all, delete-orphan")

    def to_dict(self):
        return {
            "image_id": self.image_id,
            "user_id": self.user_id,
            "username": self.user.username if self.user else None,
            "avatar": self.user.avatar if self.user else None,
            "storage_path": self.storage_path,
            "like_count": self.like_count,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "title": self.info.title if self.info else None,
            "content": self.info.content if self.info else None,
            "audit_status": self.audit_status
        }


class ImageInfo(Base):
    __tablename__ = "image_info"
    
    image_id = Column(Integer, ForeignKey("image_data.image_id", ondelete="CASCADE"), primary_key=True)
    title = Column(String(80), nullable=False)
    content = Column(String(2000), nullable=False)
    
    # 关系
    image = relationship("ImageData", back_populates="info")
    
    def to_dict(self):
        return {
            "image_id": self.image_id,
            "title": self.title,
            "content": self.content
        }


class UserImageLike(Base):
    __tablename__ = "user_image_like"
    
    like_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("user_account.user_id"), nullable=False)
    image_id = Column(Integer, ForeignKey("image_data.image_id"), nullable=False)
    liked_status = Column(Integer, default=1, nullable=False)  # 1表示点赞，0表示取消
    
    # 关系
    user = relationship("UserAccount", back_populates="liked_images")
    image = relationship("ImageData", back_populates="likes")
    
    def to_dict(self):
        return {
            "like_id": self.like_id,
            "user_id": self.user_id,
            "image_id": self.image_id,
            "liked_status": self.liked_status
        }

class Comment(Base):
    __tablename__ = "comments"
    
    comment_id = Column(Integer, primary_key=True, autoincrement=True)
    image_id = Column(Integer, ForeignKey("image_data.image_id"), nullable=False)
    user_id = Column(Integer, ForeignKey("user_account.user_id"), nullable=False)
    parent_comment_id = Column(Integer, ForeignKey("comments.comment_id"), nullable=True)
    content = Column(Text, nullable=False)
    created_at = Column(DateTime, default=func.now())
    
    # 关系
    user = relationship("UserAccount", back_populates="comments")
    image = relationship("ImageData", back_populates="comments")
    # 自引用关系
    replies = relationship("Comment", backref="parent_comment", remote_side=[comment_id])
    
    def to_dict(self):
        return {
            "comment_id": self.comment_id,
            "image_id": self.image_id,
            "user_id": self.user_id,
            "username": self.user.username if self.user else None,
            "avatar": self.user.avatar if self.user else None,
            "parent_comment_id": self.parent_comment_id,
            "content": self.content,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

# 添加私信相关模型
class Conversation(Base):
    __tablename__ = "conversation"
    
    conversation_id = Column(String(36), primary_key=True)
    user1_id = Column(Integer, ForeignKey("user_account.user_id"), nullable=False)
    user2_id = Column(Integer, ForeignKey("user_account.user_id"), nullable=False)
    latest_message_id = Column(Integer, nullable=True)
    
    # 关系
    user1 = relationship("UserAccount", foreign_keys=[user1_id], back_populates="conversations1")
    user2 = relationship("UserAccount", foreign_keys=[user2_id], back_populates="conversations2")
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")
    
    def to_dict(self):
        return {
            "conversation_id": self.conversation_id,
            "user1_id": self.user1_id,
            "user2_id": self.user2_id,
            "latest_message_id": self.latest_message_id
        }

class Message(Base):
    __tablename__ = "message"
    
    message_id = Column(Integer, primary_key=True, autoincrement=True)
    conversation_id = Column(String(36), ForeignKey("conversation.conversation_id"), nullable=False)
    sender_id = Column(Integer, ForeignKey("user_account.user_id"), nullable=False)
    receiver_id = Column(Integer, ForeignKey("user_account.user_id"), nullable=False)
    content = Column(Text, nullable=False)
    status = Column(Integer, default=1, comment="1未读 2已读 3发送方删除 4接收方删除")
    created_at = Column(TIMESTAMP, default=func.now())
    
    # 关系
    conversation = relationship("Conversation", back_populates="messages")
    sender = relationship("UserAccount", foreign_keys=[sender_id], back_populates="sent_messages")
    receiver = relationship("UserAccount", foreign_keys=[receiver_id], back_populates="received_messages")
    
    def to_dict(self):
        return {
            "message_id": self.message_id,
            "conversation_id": self.conversation_id,
            "sender_id": self.sender_id,
            "receiver_id": self.receiver_id,
            "content": self.content,
            "status": self.status,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class DeviceToken(Base):
    __tablename__ = "device_token"

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("user_account.user_id"), nullable=False)
    device_token = Column(String(255), nullable=False, unique=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(TIMESTAMP, default=func.now())
    updated_at = Column(TIMESTAMP, default=func.now(), onupdate=func.now())

    # 关系
    user = relationship("UserAccount", back_populates="device_tokens")

    def to_dict(self):
        return {
            "id": self.id,
            "user_id": self.user_id,
            "device_token": self.device_token,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

class UserConversationMute(Base):
    __tablename__ = "user_conversation_mute"

    user_id = Column(Integer, ForeignKey("user_account.user_id"), primary_key=True)
    conversation_id = Column(String(36), ForeignKey("conversation.conversation_id"), primary_key=True)
    is_muted = Column(Boolean, default=False, nullable=False, comment="是否开启免打扰，默认关闭")

    # 关系
    user = relationship("UserAccount")
    conversation = relationship("Conversation")

    def to_dict(self):
        return {
            "user_id": self.user_id,
            "conversation_id": self.conversation_id,
            "is_muted": self.is_muted
        }