<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>logFormatVersion</key>
	<integer>11</integer>
	<key>logs</key>
	<dict>
		<key>43E9EFFD-2641-4154-A54B-85761D825AB1</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>43E9EFFD-2641-4154-A54B-85761D825AB1.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>S</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>0</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>FileCopyUtility project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>FileCopyUtility</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Cleaning project FileCopyUtility with scheme FileCopyUtility and configuration Release</string>
			<key>timeStartedRecording</key>
			<real>777179643.95898199</real>
			<key>timeStoppedRecording</key>
			<real>777179644.20095098</real>
			<key>title</key>
			<string>Cleaning project FileCopyUtility with scheme FileCopyUtility and configuration Release</string>
			<key>uniqueIdentifier</key>
			<string>43E9EFFD-2641-4154-A54B-85761D825AB1</string>
		</dict>
		<key>9AD4F93E-2351-4889-9BF3-85FDF5B24C8F</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>9AD4F93E-2351-4889-9BF3-85FDF5B24C8F.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>W</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>2</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>FileCopyUtility project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>FileCopyUtility</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Building project FileCopyUtility with scheme FileCopyUtility and configuration Release</string>
			<key>timeStartedRecording</key>
			<real>777179644.20287299</real>
			<key>timeStoppedRecording</key>
			<real>777179649.27332497</real>
			<key>title</key>
			<string>Building project FileCopyUtility with scheme FileCopyUtility and configuration Release</string>
			<key>uniqueIdentifier</key>
			<string>9AD4F93E-2351-4889-9BF3-85FDF5B24C8F</string>
		</dict>
		<key>A83D2FC6-40DB-4992-9B68-E521B0720D6D</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>A83D2FC6-40DB-4992-9B68-E521B0720D6D.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>S</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>0</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>FileCopyUtility project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>FileCopyUtility</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Cleaning project FileCopyUtility with scheme FileCopyUtility and configuration Release</string>
			<key>timeStartedRecording</key>
			<real>777178361.12024498</real>
			<key>timeStoppedRecording</key>
			<real>777178361.28332496</real>
			<key>title</key>
			<string>Cleaning project FileCopyUtility with scheme FileCopyUtility and configuration Release</string>
			<key>uniqueIdentifier</key>
			<string>A83D2FC6-40DB-4992-9B68-E521B0720D6D</string>
		</dict>
	</dict>
</dict>
</plist>
