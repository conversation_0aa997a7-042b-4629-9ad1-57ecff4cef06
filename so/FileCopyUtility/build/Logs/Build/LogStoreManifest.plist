<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>logFormatVersion</key>
	<integer>11</integer>
	<key>logs</key>
	<dict>
		<key>89424588-7D34-47BB-9E72-46729A4F400D</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>89424588-7D34-47BB-9E72-46729A4F400D.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>E</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>1</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>0</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>FileCopyUtility project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>FileCopyUtility</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Building project FileCopyUtility with scheme FileCopyUtility and configuration Release</string>
			<key>timeStartedRecording</key>
			<real>777178340.92993498</real>
			<key>timeStoppedRecording</key>
			<real>777178342.55572999</real>
			<key>title</key>
			<string>Building project FileCopyUtility with scheme FileCopyUtility and configuration Release</string>
			<key>uniqueIdentifier</key>
			<string>89424588-7D34-47BB-9E72-46729A4F400D</string>
		</dict>
		<key>A83D2FC6-40DB-4992-9B68-E521B0720D6D</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>A83D2FC6-40DB-4992-9B68-E521B0720D6D.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>S</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>0</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>FileCopyUtility project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>FileCopyUtility</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Cleaning project FileCopyUtility with scheme FileCopyUtility and configuration Release</string>
			<key>timeStartedRecording</key>
			<real>777178361.12024498</real>
			<key>timeStoppedRecording</key>
			<real>777178361.28332496</real>
			<key>title</key>
			<string>Cleaning project FileCopyUtility with scheme FileCopyUtility and configuration Release</string>
			<key>uniqueIdentifier</key>
			<string>A83D2FC6-40DB-4992-9B68-E521B0720D6D</string>
		</dict>
	</dict>
</dict>
</plist>
