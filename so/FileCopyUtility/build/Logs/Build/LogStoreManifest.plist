<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>logFormatVersion</key>
	<integer>11</integer>
	<key>logs</key>
	<dict>
		<key>7B0FEFC8-C3DB-49C1-955B-518FFFF16BAC</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>7B0FEFC8-C3DB-49C1-955B-518FFFF16BAC.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>S</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>0</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>FileCopyUtility project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>FileCopyUtility</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Cleaning project FileCopyUtility with scheme FileCopyUtility and configuration Release</string>
			<key>timeStartedRecording</key>
			<real>777180595.40111399</real>
			<key>timeStoppedRecording</key>
			<real>777180595.65458405</real>
			<key>title</key>
			<string>Cleaning project FileCopyUtility with scheme FileCopyUtility and configuration Release</string>
			<key>uniqueIdentifier</key>
			<string>7B0FEFC8-C3DB-49C1-955B-518FFFF16BAC</string>
		</dict>
		<key>B3E2492A-7B10-4748-8FE9-41FBAC1D8A9A</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>B3E2492A-7B10-4748-8FE9-41FBAC1D8A9A.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>S</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>0</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>FileCopyUtility project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>FileCopyUtility</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Cleaning project FileCopyUtility with scheme FileCopyUtility and configuration Release</string>
			<key>timeStartedRecording</key>
			<real>777181762.65882301</real>
			<key>timeStoppedRecording</key>
			<real>777181762.87002206</real>
			<key>title</key>
			<string>Cleaning project FileCopyUtility with scheme FileCopyUtility and configuration Release</string>
			<key>uniqueIdentifier</key>
			<string>B3E2492A-7B10-4748-8FE9-41FBAC1D8A9A</string>
		</dict>
	</dict>
</dict>
</plist>
