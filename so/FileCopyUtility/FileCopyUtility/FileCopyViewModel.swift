import SwiftUI
import CryptoKit
import UniformTypeIdentifiers
import Foundation

class FileCopyViewModel: ObservableObject {
    @Published var sourcePath: String = ""
    @Published var destinationPaths: [String] = []
    @Published var destinationFileNames: [String] = [] // 新增：目标文件名数组
    @Published var progress: Double = 0
    @Published var isProcessing: Bool = false
    @Published var statusMessage: String = "就绪"
    @Published var failedFiles: [String] = []
    @Published var currentPhase: ProcessPhase = .idle
    
    // 添加用于存储调整后的目标文件夹名称
    private var adjustedDestinationFolderName: String? = nil
    
    // 添加防止App在屏幕锁定时暂停的属性
    private var backgroundActivity: NSObjectProtocol? = nil
    
    enum ProcessPhase {
        case idle
        case copying
        case verifying
    }
    
    private var totalFiles: Int = 0
    private var processedFiles: Int = 0
    private var currentDestinationIndex: Int = 0
    private var filesToProcess: [URL] = []
    private var filesToVerify: [URL] = [] // 单独存储需要校验的文件列表
    private var cancelRequested: Bool = false
    private var isSourceDirectory: Bool = true
    private var sourceDirectoryURL: URL?
    
    // 用于基于大小的进度计算
    private var totalBytesToCopy: UInt64 = 0
    private var copiedBytes: UInt64 = 0
    
    // 用于速度计算
    private var lastSpeedUpdateTime: Date = Date()
    private var lastCopiedBytes: UInt64 = 0
    private var currentSpeed: Double = 0 // 当前速度，单位：字节/秒
    
    // 获取CPU核心数并计算线程数
    private let concurrentThreads: Int = {
        let processorCount = ProcessInfo.processInfo.processorCount
        let threadCount = max(2, processorCount) // 至少保留2个线程
        return threadCount
    }()
    
    // 缓冲区大小调整为较小的值，减少内存占用
    private let fileReadBufferSize: Int = 10240 * 1024 // 10MB缓冲区
    
    // 小文件批处理大小 (当复制小文件时使用批处理以提高性能)
    // 增大此值可能提高性能（如果内存足够），但会增加内存使用
    // 根据系统配置，可以尝试调整此值（100-500范围）来获得最佳性能
    private let smallFileBatchSize: Int = 100
    private let smallFileThreshold: Int = 4*1024 * 1024 // 4MB以下视为小文件
    private let maxRetryCount: Int = 1 // 校验失败时的最大重试次数
    
    private let fileManager = FileManager.default
    private var copyTask: Task<Void, Error>? = nil
    
    var activeDestinationPaths: [String] {
        return destinationPaths.filter { !$0.isEmpty }
    }

    // 获取源文件名
    func getSourceFileName() -> String {
        if sourcePath.isEmpty {
            return ""
        }
        return URL(fileURLWithPath: sourcePath).lastPathComponent
    }

    // 设置目标文件名
    func setDestinationFileName(at index: Int, fileName: String) {
        while destinationFileNames.count <= index {
            destinationFileNames.append("")
        }
        destinationFileNames[index] = fileName
    }

    // 获取目标文件名，如果没有设置则返回源文件名
    func getDestinationFileName(at index: Int) -> String {
        if destinationFileNames.count > index && !destinationFileNames[index].isEmpty {
            return destinationFileNames[index]
        }
        return getSourceFileName()
    }
    
    // 选择源文件夹
    func selectSourceFolder() {
        let panel = NSOpenPanel()
        panel.allowsMultipleSelection = false
        panel.canChooseDirectories = true
        panel.canChooseFiles = true
        panel.canCreateDirectories = true // 允许创建新文件夹
        panel.showsHiddenFiles = false    // 不显示隐藏文件
        panel.showsTagField = false       // 不显示标签字段
        panel.canDownloadUbiquitousContents = true // 允许访问iCloud内容
        panel.treatsFilePackagesAsDirectories = false
        panel.title = "选择源文件或文件夹"
        
        if panel.runModal() == .OK {
            if let url = panel.url {
                self.sourcePath = url.path
                self.sourceDirectoryURL = url
                // 检查是否是目录
                var isDir: ObjCBool = false
                if fileManager.fileExists(atPath: url.path, isDirectory: &isDir) {
                    self.isSourceDirectory = isDir.boolValue
                }

                // 更新所有目标文件名为新的源文件名
                let sourceFileName = url.lastPathComponent
                for i in 0..<destinationFileNames.count {
                    if destinationFileNames[i].isEmpty || destinationFileNames[i] == getSourceFileName() {
                        destinationFileNames[i] = sourceFileName
                    }
                }
            }
        }
    }
    
    // 选择目标文件夹
    func selectDestinationFolder(at index: Int) {
        let panel = NSOpenPanel()
        panel.allowsMultipleSelection = false
        panel.canChooseDirectories = true
        panel.canChooseFiles = false
        panel.canCreateDirectories = true // 允许创建新文件夹
        panel.showsHiddenFiles = false    // 不显示隐藏文件
        panel.showsTagField = false       // 不显示标签字段
        panel.canDownloadUbiquitousContents = true // 允许访问iCloud内容
        panel.treatsFilePackagesAsDirectories = false
        panel.title = "选择目标文件夹"
        panel.prompt = "选择" // 修改确认按钮文字
        
        if panel.runModal() == .OK {
            if let url = panel.url {
                while destinationPaths.count <= index {
                    destinationPaths.append("")
                }
                destinationPaths[index] = url.path
            }
        }
    }
    
    // 清除所有
    func clearAll() {
        sourcePath = ""
        destinationPaths = []
        destinationFileNames = [] // 清除目标文件名
        progress = 0
        failedFiles = []
        statusMessage = "就绪"
        currentPhase = .idle
    }
    
    // 取消操作
    func cancelOperation() {
        cancelRequested = true
        copyTask?.cancel()
        statusMessage = "操作已取消"
        isProcessing = false
        currentPhase = .idle
        
        // 结束后台活动
        endBackgroundActivity()
    }
    
    // 开始复制
    func startCopy() {
        guard !sourcePath.isEmpty, !activeDestinationPaths.isEmpty, let sourceURL = sourceDirectoryURL else { return }
        
        // 开始后台活动，防止屏幕锁定导致操作暂停
        startBackgroundActivity()
        
        // 重置状态
        isProcessing = true
        progress = 0
        failedFiles = []
        cancelRequested = false
        currentDestinationIndex = 0
        currentPhase = .copying
        filesToVerify = [] // 清空校验文件列表
        totalBytesToCopy = 0
        copiedBytes = 0
        currentSpeed = 0
        lastCopiedBytes = 0
        adjustedDestinationFolderName = nil // 重置调整后的文件夹名称
        
        // 注意: 本程序实现了智能文件复制功能：
        // 1. 复制前会检查目标路径是否存在同名文件/文件夹
        // 2. 如果存在，会自动在文件名/文件夹名后添加当前时间戳 (格式：yyyyMMddHHmm)
        // 3. 所有操作（复制、校验、重试）都会使用这个带时间戳的名称
        // 4. 这样可以避免覆盖目标位置的原有文件
        
        // 获取所有需要复制的文件
        filesToProcess = []
        
        do {
            if isSourceDirectory {
                // 递归获取所有文件，但不排除目录
                guard let enumerator = fileManager.enumerator(at: sourceURL, includingPropertiesForKeys: [.isDirectoryKey, .fileSizeKey]) else {
                    throw NSError(domain: "FileCopyUtility", code: 1, userInfo: [NSLocalizedDescriptionKey: "无法枚举源目录文件"])
                }
                
                while let fileURL = enumerator.nextObject() as? URL {
                    // 将所有项目添加到处理列表中（包括目录）
                    filesToProcess.append(fileURL)
                    
                    // 同时收集非目录文件，用于后续校验
                    do {
                        let resourceValues = try fileURL.resourceValues(forKeys: [.isDirectoryKey, .fileSizeKey])
                        if let isDirectory = resourceValues.isDirectory, !isDirectory {
                            filesToVerify.append(fileURL)
                            
                            // 获取并累加文件大小用于进度计算
                            if let fileSize = resourceValues.fileSize {
                                totalBytesToCopy += UInt64(fileSize)
                            }
                        }
                    } catch {
                        // 忽略错误，最坏情况只是在校验阶段需要重新筛选
                    }
                }
            } else {
                // 如果是单个文件，直接添加
                filesToProcess.append(sourceURL)
                filesToVerify.append(sourceURL)
                
                // 获取单个文件的大小
                do {
                    let attributes = try fileManager.attributesOfItem(atPath: sourceURL.path)
                    if let fileSize = attributes[.size] as? UInt64 {
                        totalBytesToCopy = fileSize
                    }
                } catch {
                    // 忽略错误
                }
            }
            
            totalFiles = filesToProcess.count
            statusMessage = "发现 \(totalFiles) 个项目，准备复制到第一个目标 (总大小: \(formatFileSize(totalBytesToCopy)))"
            
            // 异步执行复制
            copyTask = Task {
                do {
                    // 启动进度更新定时器，确保UI能实时响应
                    startProgressUpdateTimer()
                    
                    try await processCopyToNextDestination()
                    
                    // 停止进度更新定时器
                    stopProgressUpdateTimer()
                } catch {
                    // 停止进度更新定时器
                    stopProgressUpdateTimer()
                    
                    await MainActor.run {
                        statusMessage = "复制过程发生错误: \(error.localizedDescription)"
                        isProcessing = false
                        currentPhase = .idle
                    }
                }
            }
        } catch {
            statusMessage = "获取文件列表失败: \(error.localizedDescription)"
            isProcessing = false
            currentPhase = .idle
        }
    }
    
    // 定时更新进度的定时器
    private var progressUpdateTimer: Timer?
    private var lastUpdateTime: Date = Date()
    private var needsProgressUpdate: Bool = false
    
    // 启动进度更新定时器 - 每0.2秒更新一次UI
    private func startProgressUpdateTimer() {
        stopProgressUpdateTimer() // 确保没有正在运行的定时器
        
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.lastUpdateTime = Date()
            self.lastSpeedUpdateTime = Date()
            self.lastCopiedBytes = 0
            self.currentSpeed = 0
            self.progressUpdateTimer = Timer.scheduledTimer(withTimeInterval: 0.2, repeats: true) { [weak self] _ in
                guard let self = self else { return }
                
                if self.needsProgressUpdate || Date().timeIntervalSince(self.lastUpdateTime) >= 0.5 {
                    // 更新UI - 使用MainActor.run确保在主线程上运行
                    Task { @MainActor in
                        self.updateProgressUI()
                        self.needsProgressUpdate = false
                        self.lastUpdateTime = Date()
                    }
                }
            }
        }
    }
    
    // 停止进度更新定时器
    private func stopProgressUpdateTimer() {
        DispatchQueue.main.async { [weak self] in
            self?.progressUpdateTimer?.invalidate()
            self?.progressUpdateTimer = nil
        }
    }
    
    // 更新进度UI
    @MainActor
    private func updateProgressUI() {
        if currentPhase == .copying {
            // 复制阶段：使用已复制字节数计算进度
            if totalBytesToCopy > 0 {
                self.progress = min(1.0, Double(self.copiedBytes) / Double(self.totalBytesToCopy))
            } else if totalFiles > 0 {
                self.progress = Double(self.processedFiles) / Double(self.totalFiles)
            }
            
            // 计算复制速度
            calculateCopySpeed()
            
            // 更新状态消息
            if processedFiles > 0 && totalFiles > 0 {
                let speedText = currentSpeed > 0 ? " - \(formatSpeed(currentSpeed))" : ""
                self.statusMessage = "正在复制文件 \(self.processedFiles)/\(self.totalFiles)（\(self.formatFileSize(self.copiedBytes))/\(self.formatFileSize(self.totalBytesToCopy))）到目标 \(self.currentDestinationIndex + 1)\(speedText)"
            }
        } else if currentPhase == .verifying {
            // 校验阶段：同样使用已校验字节数计算进度
            // 注意：在校验阶段，copiedBytes被重用来表示已校验的字节数
            if totalBytesToCopy > 0 {
                self.progress = min(1.0, Double(self.copiedBytes) / Double(self.totalBytesToCopy))
            } else if totalFiles > 0 {
                self.progress = Double(self.processedFiles) / Double(self.totalFiles)
            }
            
            // 计算校验速度
            calculateCopySpeed()
            
            // 更新状态信息，包括校验速度
            if processedFiles > 0 && filesToVerify.count > 0 {
                let speedText = currentSpeed > 0 ? " - \(formatSpeed(currentSpeed))" : ""
                self.statusMessage = "已校验 \(self.processedFiles)/\(filesToVerify.count) 文件 (\(self.formatFileSize(self.copiedBytes))/\(self.formatFileSize(self.totalBytesToCopy)))\(speedText)"
            }
        }
    }
    
    // 计算当前复制/校验速度
    private func calculateCopySpeed() {
        let now = Date()
        let timeInterval = now.timeIntervalSince(lastSpeedUpdateTime)
        
        // 至少等待0.5秒才更新速度，防止频繁波动
        if timeInterval >= 0.5 {
            let bytesCopiedSinceLastUpdate = copiedBytes > lastCopiedBytes ? copiedBytes - lastCopiedBytes : 0
            
            if timeInterval > 0 && bytesCopiedSinceLastUpdate > 0 {
                // 计算字节/秒
                currentSpeed = Double(bytesCopiedSinceLastUpdate) / timeInterval
            } else {
                currentSpeed = 0
            }
            
            // 更新上次更新时间和字节数
            lastSpeedUpdateTime = now
            lastCopiedBytes = copiedBytes
        }
    }
    
    // 格式化速度为人类可读格式
    private func formatSpeed(_ bytesPerSecond: Double) -> String {
        let gigabyte = 1024.0 * 1024.0 * 1024.0
        let megabyte = 1024.0 * 1024.0
        let kilobyte = 1024.0
        
        switch bytesPerSecond {
        case let speed where speed >= gigabyte:
            return String(format: "%.2f GB/s", speed / gigabyte)
        case let speed where speed >= megabyte:
            return String(format: "%.2f MB/s", speed / megabyte)
        case let speed where speed >= kilobyte:
            return String(format: "%.2f KB/s", speed / kilobyte)
        default:
            return "\(Int(bytesPerSecond)) B/s"
        }
    }
    
    // 标记需要更新进度，在下一个定时器周期会更新UI
    private func markProgressNeedsUpdate() {
        self.needsProgressUpdate = true
    }
    
    // 格式化文件大小为人类可读格式
    private func formatFileSize(_ bytes: UInt64) -> String {
        let gigabyte = 1024.0 * 1024.0 * 1024.0
        let megabyte = 1024.0 * 1024.0
        let kilobyte = 1024.0
        
        switch bytes {
        case let size where size >= UInt64(gigabyte):
            return String(format: "%.2f GB", Double(size) / gigabyte)
        case let size where size >= UInt64(megabyte):
            return String(format: "%.2f MB", Double(size) / megabyte)
        case let size where size >= UInt64(kilobyte):
            return String(format: "%.2f KB", Double(size) / kilobyte)
        default:
            return "\(bytes) B"
        }
    }
    
    // 生成带时间戳的文件/文件夹名称
    private func generateTimestampedName(originalName: String) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMddHHmm"
        let timestamp = dateFormatter.string(from: Date())
        return "\(originalName)_\(timestamp)"
    }
    
    // 检查目标路径是否存在同名文件/文件夹，如果存在则返回带时间戳的名称
    private func checkAndGetAdjustedName(sourceURL: URL, destinationBaseURL: URL, customFileName: String? = nil) -> String {
        // 使用自定义文件名或源文件名
        let targetName = customFileName ?? sourceURL.lastPathComponent
        let targetPath = destinationBaseURL.appendingPathComponent(targetName).path

        // 检查目标路径是否已存在同名文件/文件夹
        if fileManager.fileExists(atPath: targetPath) {
            // 如果存在，生成带时间戳的名称
            return generateTimestampedName(originalName: targetName)
        }

        // 如果不存在，返回目标名称
        return targetName
    }
    
    // 处理复制到下一个目标
    @MainActor
    private func processCopyToNextDestination() async throws {
        guard currentDestinationIndex < activeDestinationPaths.count, let sourceURL = sourceDirectoryURL else {
            statusMessage = "所有复制和校验操作已完成"
            isProcessing = false
            currentPhase = .idle
            return
        }
        
        let currentDestination = activeDestinationPaths[currentDestinationIndex]
        let destinationBaseURL = URL(fileURLWithPath: currentDestination)
        
        // 在复制前检查目标路径是否存在同名文件/文件夹，并调整目标文件夹名称
        let customFileName = getDestinationFileName(at: currentDestinationIndex)
        if isSourceDirectory {
            // 对于目录，检查目标位置是否已有同名目录
            adjustedDestinationFolderName = checkAndGetAdjustedName(sourceURL: sourceURL, destinationBaseURL: destinationBaseURL, customFileName: customFileName)
        } else {
            // 对于单个文件，使用自定义文件名检查目标位置是否已有同名文件
            adjustedDestinationFolderName = checkAndGetAdjustedName(sourceURL: sourceURL, destinationBaseURL: destinationBaseURL, customFileName: customFileName)
        }
        
        // 完全重置进度变量，确保每个目标都显示正确的进度
        processedFiles = 0
        progress = 0
        copiedBytes = 0 // 重置已复制的字节数
        currentPhase = .copying
        currentSpeed = 0
        lastCopiedBytes = 0
        lastSpeedUpdateTime = Date()
        
        statusMessage = "正在复制到目标 \(currentDestinationIndex + 1): \(currentDestination)"
        
        // 确保进度更新定时器正常工作
        stopProgressUpdateTimer()
        startProgressUpdateTimer()
        
        if isSourceDirectory {
            // 如果是目录，先创建目标位置的源目录
            let sourceDirName = adjustedDestinationFolderName ?? sourceURL.lastPathComponent
            let targetDirURL = destinationBaseURL.appendingPathComponent(sourceDirName)
            
            do {
                // 如果目标文件夹已存在，先删除
                if fileManager.fileExists(atPath: targetDirURL.path) {
                    try fileManager.removeItem(at: targetDirURL)
                }
                
                // 创建目标根目录
                try fileManager.createDirectory(at: targetDirURL, withIntermediateDirectories: true)
            } catch {
                failedFiles.append("\(sourceDirName) - 创建目录失败: \(error.localizedDescription)")
                throw error
            }
            
            // 复制所有文件和目录 - 使用异步迭代防止UI卡顿
            let (nonDirectoryFilesAndSizes, directoryFiles) = await classifyFiles(from: filesToProcess, sourceURL: sourceURL, targetDirURL: targetDirURL)
            
            // 先创建所有目录结构
            for (fileURL, destinationFileURL, _) in directoryFiles {
                if cancelRequested { break }
                
                // 创建目录结构
                do {
                    if !fileManager.fileExists(atPath: destinationFileURL.path) {
                        try fileManager.createDirectory(at: destinationFileURL, withIntermediateDirectories: true)
                    }
                } catch {
                    await MainActor.run {
                        failedFiles.append("\(fileURL.lastPathComponent) - 创建目录失败: \(error.localizedDescription)")
                    }
                }
                
                // 让UI有机会更新
                await Task.yield()
            }
            
            // 确保所有文件的父目录都存在
            for (fileURL, destinationFileURL, _, _) in nonDirectoryFilesAndSizes {
                if cancelRequested { break }
                
                // 确保目录结构已创建
                do {
                    let directory = destinationFileURL.deletingLastPathComponent()
                    if !fileManager.fileExists(atPath: directory.path) {
                        try fileManager.createDirectory(at: directory, withIntermediateDirectories: true)
                    }
                } catch {
                    await MainActor.run {
                        failedFiles.append("\(fileURL.lastPathComponent) - 创建目录失败: \(error.localizedDescription)")
                    }
                }
            }
            
            // 然后复制所有非目录文件，区分大文件和小文件不同处理策略
            let totalNonDirFiles = nonDirectoryFilesAndSizes.count
            
            // 将文件按大小分组处理
            let (smallFiles, normalFiles) = partitionFilesBySize(nonDirectoryFilesAndSizes)
            
            await MainActor.run {
                self.statusMessage = "正在复制文件（共\(totalNonDirFiles)个，总大小\(formatFileSize(totalBytesToCopy))）到目标 \(self.currentDestinationIndex + 1)"
            }
            
            // 进度跟踪
            var copiedFiles = 0
            
            // 1. 处理普通大小的文件 - 使用并行任务单独处理
            if !normalFiles.isEmpty {
                await copyNormalFiles(normalFiles, totalFiles: totalNonDirFiles, copiedCount: &copiedFiles)
            }
            
            // 2. 批量处理小文件 - 提高小文件复制性能
            if !smallFiles.isEmpty {
                await copySmallFilesInBatches(smallFiles, totalFiles: totalNonDirFiles, copiedCount: &copiedFiles)
            }
            
            // 确保进度达到100%
            await MainActor.run {
                self.processedFiles = totalNonDirFiles
                self.progress = 1.0
            }
            
        } else {
            // 单个文件复制
            let destinationFileName = adjustedDestinationFolderName ?? sourceURL.lastPathComponent
            let destinationFileURL = destinationBaseURL.appendingPathComponent(destinationFileName)
            
            // 在后台线程执行复制，添加try
            do {
                try await Task.detached(priority: .userInitiated) {
                    do {
                        // 如果目标文件已存在，先删除
                        if self.fileManager.fileExists(atPath: destinationFileURL.path) {
                            try self.fileManager.removeItem(at: destinationFileURL)
                        }
                        
                        // 复制文件
                        try self.fileManager.copyItem(at: sourceURL, to: destinationFileURL)
                        
                        // 更新进度
                        await MainActor.run {
                            self.processedFiles = 1
                            self.copiedBytes = self.totalBytesToCopy
                            self.progress = 1.0
                            self.statusMessage = "已复制文件到目标 \(self.currentDestinationIndex + 1)"
                        }
                    } catch {
                        await MainActor.run {
                            self.failedFiles.append("\(sourceURL.lastPathComponent) - 复制失败: \(error.localizedDescription)")
                        }
                        throw error
                    }
                }.value
            } catch {
                await MainActor.run {
                    self.failedFiles.append("\(sourceURL.lastPathComponent) - 任务执行失败: \(error.localizedDescription)")
                }
                throw error
            }
        }
        
        // 开始校验文件
        if !cancelRequested {
            // 切换到校验阶段并重置进度
            await MainActor.run {
                self.progress = 0
                self.copiedBytes = 0
                self.processedFiles = 0
                self.currentPhase = .verifying
            }
            await verifyFiles()
        }
    }
    
    // 获取文件列表,准备复制 - 返回目录文件和非目录文件(带大小)
    private func classifyFiles(from files: [URL], sourceURL: URL, targetDirURL: URL) async -> ([(URL, URL, String, UInt64)], [(URL, URL, String)]) {
        var nonDirectoryFiles: [(URL, URL, String, UInt64)] = []
        var directoryFiles: [(URL, URL, String)] = []
        
        for fileURL in files {
            let relativePath = fileURL.path.replacingOccurrences(of: sourceURL.path, with: "")
            let destinationFileURL = targetDirURL.appendingPathComponent(relativePath)
            
            do {
                let resourceValues = try fileURL.resourceValues(forKeys: [.isDirectoryKey, .fileSizeKey])
                
                if let isDirectory = resourceValues.isDirectory, isDirectory {
                    directoryFiles.append((fileURL, destinationFileURL, relativePath))
                } else {
                    // 获取文件大小，如果获取失败则默认为0
                    let fileSize = resourceValues.fileSize ?? 0
                    nonDirectoryFiles.append((fileURL, destinationFileURL, relativePath, UInt64(fileSize)))
                }
            } catch {
                // 如果无法获取资源值，假设它不是目录
                nonDirectoryFiles.append((fileURL, destinationFileURL, relativePath, 0))
            }
            
            // 定期让出控制权给UI线程
            if (nonDirectoryFiles.count + directoryFiles.count) % 100 == 0 {
                await Task.yield()
            }
        }
        
        return (nonDirectoryFiles, directoryFiles)
    }
    
    // 根据文件大小分组
    private func partitionFilesBySize(_ files: [(URL, URL, String, UInt64)]) -> (small: [(URL, URL, String, UInt64)], normal: [(URL, URL, String, UInt64)]) {
        var smallFiles: [(URL, URL, String, UInt64)] = []
        var normalFiles: [(URL, URL, String, UInt64)] = []
        
        for file in files {
            if file.3 <= smallFileThreshold {
                smallFiles.append(file)
            } else {
                normalFiles.append(file)
            }
        }
        
        return (smallFiles, normalFiles)
    }
    
    // 复制普通大小的文件
    private func copyNormalFiles(_ files: [(URL, URL, String, UInt64)], totalFiles: Int, copiedCount: inout Int) async {
        // 创建固定数量的工作线程
        var localCopiedCount = 0
        
        await withTaskGroup(of: (Int, UInt64).self) { group in
            // 将文件平均分配到工作线程
            let filesPerThread = max(1, files.count / concurrentThreads)
            
            for threadIndex in 0..<min(concurrentThreads, files.count) {
                let startIndex = threadIndex * filesPerThread
                let endIndex = min(startIndex + filesPerThread, files.count)
                
                if startIndex < endIndex {
                    group.addTask {
                        var threadCopiedCount = 0
                        var threadCopiedBytes: UInt64 = 0
                        
                        for i in startIndex..<endIndex {
                            if self.cancelRequested { break }
                            
                            let (fileURL, destinationFileURL, _, fileSize) = files[i]
                            
                            do {
                                // 如果目标文件已存在，先删除
                                if self.fileManager.fileExists(atPath: destinationFileURL.path) {
                                    try self.fileManager.removeItem(at: destinationFileURL)
                                }
                                
                                // 对于大文件采用分块复制并更新进度
                                if fileSize > self.smallFileThreshold {
                                    // 使用分块复制，并在过程中更新进度
                                    try await self.copyLargeFileWithProgress(from: fileURL, to: destinationFileURL, fileSize: fileSize)
                                } else {
                                    // 小文件使用系统方法直接复制
                                    try self.fileManager.copyItem(at: fileURL, to: destinationFileURL)
                                    
                                    // 每完成一个小文件立即更新进度
                                    await MainActor.run {
                                        self.processedFiles += 1
                                        self.copiedBytes += fileSize
                                        self.markProgressNeedsUpdate()
                                    }
                                }
                                
                                threadCopiedCount += 1
                                threadCopiedBytes += fileSize
                            } catch {
                                await MainActor.run {
                                    if !self.failedFiles.contains(fileURL.lastPathComponent) {
                                        self.failedFiles.append("\(fileURL.lastPathComponent) - 复制失败: \(error.localizedDescription)")
                                    }
                                }
                            }
                            
                            // 每隔一些文件让出执行权
                            if i % 20 == 0 {
                                await Task.yield()
                            }
                        }
                        
                        return (threadCopiedCount, threadCopiedBytes)
                    }
                }
            }
            
            // 等待所有线程完成并收集结果
            for await (count, _) in group {
                localCopiedCount += count
            }
        }
        
        // 在所有异步任务完成后，更新外部计数
        copiedCount += localCopiedCount
    }
    
    // 用于大文件的分块复制，支持进度更新
    private func copyLargeFileWithProgress(from sourceURL: URL, to destinationURL: URL, fileSize: UInt64) async throws {
        let sourceHandle = try FileHandle(forReadingFrom: sourceURL)
        defer { try? sourceHandle.close() }
        
        // 创建目标文件
        fileManager.createFile(atPath: destinationURL.path, contents: nil)
        let destinationHandle = try FileHandle(forWritingTo: destinationURL)
        defer { try? destinationHandle.close() }
        
        // 使用较大的缓冲区来读写文件
        let bufferSize = min(fileReadBufferSize, 50 * 1024 * 1024) // 最大10MB的缓冲区
        var bytesProcessed: UInt64 = 0
        var totalBytesProcessed: UInt64 = 0
        let progressUpdateThreshold: UInt64 = 50 * 1024 * 1024 // 5MB
        
        // 分块读写文件
        while true {
            if cancelRequested { break }
            
            // 读取数据块
            let data = try sourceHandle.read(upToCount: bufferSize)
            
            // 如果没有更多数据则退出循环
            if data == nil || data!.isEmpty {
                break
            }
            
            // 写入数据块
            try destinationHandle.write(contentsOf: data!)
            
            // 更新已处理字节数
            let chunkSize = UInt64(data!.count)
            bytesProcessed += chunkSize
            totalBytesProcessed += chunkSize
            
            // 定期更新进度（每处理约10MB更新一次进度，避免过于频繁）
            if bytesProcessed >= progressUpdateThreshold {
                // 更新进度
                let localBytesProcessed = bytesProcessed // 创建本地副本防止并发问题
                await MainActor.run {
                    self.copiedBytes += localBytesProcessed
                    self.markProgressNeedsUpdate()
                }
                
                // 重置处理计数器，用于下一次进度更新
                bytesProcessed = 0
                
                // 允许其他任务执行
                await Task.yield()
            }
        }
        
        // 确保最后的进度也被更新
        if bytesProcessed > 0 {
            let localBytesProcessed = bytesProcessed // 创建本地副本防止并发问题
            await MainActor.run {
                self.copiedBytes += localBytesProcessed
                self.processedFiles += 1
                self.markProgressNeedsUpdate()
            }
        }
        
        // 关闭文件句柄
        try destinationHandle.close()
        try sourceHandle.close()
    }
    
    // 批量复制小文件以提高性能
    private func copySmallFilesInBatches(_ files: [(URL, URL, String, UInt64)], totalFiles: Int, copiedCount: inout Int) async {
        // 将小文件分成多个批次
        let batchCount = max(1, files.count / smallFileBatchSize)
        let batchSize = max(1, files.count / batchCount)
        var localCopiedCount = 0
        
        await withTaskGroup(of: (Int, UInt64).self) { group in
            for batchIndex in 0..<batchCount {
                let startIndex = batchIndex * batchSize
                let endIndex = min(startIndex + batchSize, files.count)
                
                if startIndex < endIndex {
                    group.addTask {
                        let batchFiles = Array(files[startIndex..<endIndex])
                        return await self.copyFileBatch(batchFiles, totalFiles: totalFiles, updateProgress: true)
                    }
                }
            }
            
            // 等待所有批次完成并收集结果
            for await (count, _) in group {
                localCopiedCount += count
            }
        }
        
        // 在所有异步任务完成后，更新外部计数
        copiedCount += localCopiedCount
    }
    
    // 复制一批小文件
    private func copyFileBatch(_ files: [(URL, URL, String, UInt64)], totalFiles: Int, updateProgress: Bool = false) async -> (Int, UInt64) {
        var batchCopiedCount = 0
        var batchCopiedBytes: UInt64 = 0
        
        for (fileURL, destinationFileURL, _, fileSize) in files {
            if cancelRequested { break }
            
            do {
                // 如果目标文件已存在，先删除
                if fileManager.fileExists(atPath: destinationFileURL.path) {
                    try fileManager.removeItem(at: destinationFileURL)
                }
                
                // 复制文件
                try fileManager.copyItem(at: fileURL, to: destinationFileURL)
                batchCopiedCount += 1
                batchCopiedBytes += fileSize
                
                // 每完成一个文件就更新进度
                if updateProgress {
                    await MainActor.run {
                        self.processedFiles += 1
                        self.copiedBytes += fileSize
                        self.markProgressNeedsUpdate()
                    }
                }
                
                // 每隔几个文件让出执行权
                if batchCopiedCount % 10 == 0 {
                    await Task.yield()
                }
            } catch {
                await MainActor.run {
                    if !self.failedFiles.contains(fileURL.lastPathComponent) {
                        self.failedFiles.append("\(fileURL.lastPathComponent) - 复制失败: \(error.localizedDescription)")
                    }
                }
            }
        }
        
        // 如果没有逐个更新进度，最后批量更新一次
        if !updateProgress {
            let localBatchCopiedCount = batchCopiedCount // 本地副本防止并发问题
            let localBatchCopiedBytes = batchCopiedBytes // 本地副本防止并发问题
            await MainActor.run {
                self.processedFiles += localBatchCopiedCount
                self.copiedBytes += localBatchCopiedBytes
                self.markProgressNeedsUpdate()
            }
        }
        
        return (batchCopiedCount, batchCopiedBytes)
    }
    
    // 校验所有文件 - 多线程并行实现，增加校验失败时的重试机制
    @MainActor
    private func verifyFiles() async {
        guard let sourceURL = sourceDirectoryURL else { return }
        
        let currentDestination = activeDestinationPaths[currentDestinationIndex]
        let destinationBaseURL = URL(fileURLWithPath: currentDestination)
        
        // 确定目标路径，使用调整后的文件夹名称
        let destinationRootURL: URL
        if isSourceDirectory {
            // 如果源是目录，目标是源目录名称文件夹（可能带有时间戳）
            let targetDirName = adjustedDestinationFolderName ?? sourceURL.lastPathComponent
            destinationRootURL = destinationBaseURL.appendingPathComponent(targetDirName)
        } else {
            // 如果源是文件，目标是目标目录
            destinationRootURL = destinationBaseURL
        }
        
        // 如果在复制过程中就已经收集了需要校验的文件，直接使用
        var filesToVerifyNow = filesToVerify
        
        // 如果没有预先收集的校验文件列表，现在收集
        if filesToVerifyNow.isEmpty {
            filesToVerifyNow = filesToProcess.filter { url in
                var isDir: ObjCBool = false
                return fileManager.fileExists(atPath: url.path, isDirectory: &isDir) && !isDir.boolValue
            }
        }
        
        // 并行校验文件
        let totalCount = filesToVerifyNow.count
        
        // 重置校验阶段的进度变量
        processedFiles = 0
        progress = 0
        copiedBytes = 0 // 复用此变量来跟踪已校验的字节数
        
        statusMessage = "正在校验目标 \(currentDestinationIndex + 1) 的文件完整性 (使用\(concurrentThreads)线程，共\(totalCount)个文件，总大小\(formatFileSize(totalBytesToCopy)))"
        
        // 重启进度更新定时器，确保校验阶段的进度也能平滑更新
        startProgressUpdateTimer()
        
        if totalCount == 0 {
            // 没有文件需要校验，直接进入下一个目标
            stopProgressUpdateTimer()
            
            // 确保进度显示为100%
            progress = 1.0
            
            // 处理下一个目标
            await processNextDestination()
            return
        }
        
        // 使用自定义Actor来管理进度更新和文件重试
        actor VerificationProgress {
            private var processedCount = 0
            private var verifiedBytes: UInt64 = 0
            private var failedItems: [String] = []
            private var filesNeedingRetry: [(sourceURL: URL, destURL: URL, fileName: String)] = []
            private let totalFilesCount: Int
            private let totalBytesCount: UInt64
            
            init(totalFilesCount: Int, totalBytesCount: UInt64) {
                self.totalFilesCount = totalFilesCount
                self.totalBytesCount = totalBytesCount
            }
            
            func incrementAndGetProgress(bytesProcessed: UInt64) -> (count: Int, bytes: UInt64, progress: Double) {
                processedCount += 1
                verifiedBytes += bytesProcessed
                let progressValue = totalBytesCount > 0 ? Double(verifiedBytes) / Double(totalBytesCount) : Double(processedCount) / Double(totalFilesCount)
                return (processedCount, verifiedBytes, progressValue)
            }
            
            func addFailedItem(_ item: String) {
                failedItems.append(item)
            }
            
            func addFileForRetry(sourceURL: URL, destURL: URL, fileName: String) {
                filesNeedingRetry.append((sourceURL, destURL, fileName))
            }
            
            func getFilesNeedingRetry() -> [(sourceURL: URL, destURL: URL, fileName: String)] {
                let files = filesNeedingRetry
                filesNeedingRetry = []
                return files
            }
            
            func getFailedItems() -> [String] {
                return failedItems
            }
            
            func getTotalVerifiedBytes() -> UInt64 {
                return verifiedBytes
            }
        }
        
        let progressTracker = VerificationProgress(totalFilesCount: totalCount, totalBytesCount: totalBytesToCopy)
        
        // 使用TaskGroup进行并行校验
        await withTaskGroup(of: Void.self) { group in
            // 将文件分组处理，每组大小基于线程数
            let filesPerBatch = max(1, (filesToVerifyNow.count + concurrentThreads - 1) / concurrentThreads)
            
            for batchStart in stride(from: 0, to: filesToVerifyNow.count, by: filesPerBatch) {
                let batchEnd = min(batchStart + filesPerBatch, filesToVerifyNow.count)
                let batch = filesToVerifyNow[batchStart..<batchEnd]
                
                group.addTask {
                    for sourceFileURL in batch {
                        if self.cancelRequested { break }
                        
                        // 计算相对路径和目标文件位置
                        let relativePath: String
                        let destinationFileURL: URL
                        
                        if self.isSourceDirectory {
                            // 如果是目录，使用相对路径
                            relativePath = sourceFileURL.path.replacingOccurrences(of: sourceURL.path, with: "")
                            destinationFileURL = destinationRootURL.appendingPathComponent(relativePath)
                        } else {
                            // 如果是单个文件，使用调整后的文件名（已包含自定义文件名逻辑）
                            let fileName = self.adjustedDestinationFolderName ?? self.getDestinationFileName(at: self.currentDestinationIndex)
                            destinationFileURL = destinationBaseURL.appendingPathComponent(fileName)
                        }
                        
                        // 校验文件是否存在
                        if !self.fileManager.fileExists(atPath: destinationFileURL.path) {
                            // 文件不存在 - 添加到重试列表
                            await progressTracker.addFileForRetry(sourceURL: sourceFileURL, destURL: destinationFileURL, fileName: sourceFileURL.lastPathComponent)
                            continue
                        }
                        
                        // 校验文件大小
                        do {
                            let sourceAttrs = try self.fileManager.attributesOfItem(atPath: sourceFileURL.path)
                            let destAttrs = try self.fileManager.attributesOfItem(atPath: destinationFileURL.path)
                            
                            // 获取文件大小用于进度计算
                            let fileSize = sourceAttrs[.size] as? UInt64 ?? 0
                            
                            if (sourceAttrs[.size] as? UInt64) != (destAttrs[.size] as? UInt64) {
                                // 文件大小不一致 - 添加到重试列表
                                await progressTracker.addFileForRetry(sourceURL: sourceFileURL, destURL: destinationFileURL, fileName: sourceFileURL.lastPathComponent)
                                continue
                            }
                            
                            // 使用优化的校验方法，避免内存堆积
                            let result = await self.verifyFileContent(sourceURL: sourceFileURL, destURL: destinationFileURL)
                            if !result.success {
                                // 文件内容不一致 - 添加到重试列表
                                await progressTracker.addFileForRetry(sourceURL: sourceFileURL, destURL: destinationFileURL, fileName: sourceFileURL.lastPathComponent)
                                continue
                            }
                            
                            // 更新进度
                            let progressInfo = await progressTracker.incrementAndGetProgress(bytesProcessed: fileSize)
                            
                            // 定期更新UI，但不要每个文件都更新，减少UI更新频率
                            if progressInfo.count % 5 == 0 || progressInfo.count == totalCount {
                                await MainActor.run {
                                    self.processedFiles = progressInfo.count
                                    self.copiedBytes = progressInfo.bytes  // 使用copiedBytes表示已校验的字节数
                                    
                                    // 使用markProgressNeedsUpdate而不是直接更新进度，避免闪烁
                                    self.markProgressNeedsUpdate()
                                    
                                    if progressInfo.count % 10 == 0 || progressInfo.count == totalCount {
                                        let speedText = self.currentSpeed > 0 ? " - \(self.formatSpeed(self.currentSpeed))" : ""
                                        self.statusMessage = "已校验 \(progressInfo.count)/\(totalCount) 文件 (\(self.formatFileSize(progressInfo.bytes))/\(self.formatFileSize(self.totalBytesToCopy)))\(speedText)"
                                    }
                                }
                            }
                        } catch {
                            // 校验出错 - 添加到重试列表
                            await progressTracker.addFileForRetry(sourceURL: sourceFileURL, destURL: destinationFileURL, fileName: sourceFileURL.lastPathComponent)
                            continue
                        }
                        
                        // 更频繁地让出控制权给系统，帮助处理内存释放
                        if self.processedFiles % 20 == 0 {
                            await Task.yield()
                        }
                    }
                }
            }
            
            // 等待所有校验任务完成
            await group.waitForAll()
        }
        
        // 处理需要重试的文件
        let filesToRetry = await progressTracker.getFilesNeedingRetry()
        if !filesToRetry.isEmpty {
            // 重置进度计数器，准备开始重试复制
            processedFiles = 0
            progress = 0
            
            statusMessage = "发现\(filesToRetry.count)个文件校验失败，正在尝试重新复制..."
            
            // 重新复制失败的文件
            for (index, (sourceURL, destURL, fileName)) in filesToRetry.enumerated() {
                if cancelRequested { break }
                
                do {
                    // 如果目标文件已存在，先删除
                    if fileManager.fileExists(atPath: destURL.path) {
                        try fileManager.removeItem(at: destURL)
                    }
                    
                    // 确保目标目录存在
                    let directory = destURL.deletingLastPathComponent()
                    if !fileManager.fileExists(atPath: directory.path) {
                        try fileManager.createDirectory(at: directory, withIntermediateDirectories: true)
                    }
                    
                    // 重新复制文件
                    try fileManager.copyItem(at: sourceURL, to: destURL)
                    
                    // 获取文件大小用于进度更新
                    var fileSize: UInt64 = 0
                    do {
                        let attributes = try fileManager.attributesOfItem(atPath: sourceURL.path)
                        if let size = attributes[.size] as? UInt64 {
                            fileSize = size
                        }
                    } catch {
                        // 忽略错误
                    }
                    
                    // 再次校验
                    let result = await verifyFileContent(sourceURL: sourceURL, destURL: destURL)
                    if !result.success {
                        // 重试后依然失败
                        if let errorMessage = result.errorMessage {
                            await progressTracker.addFailedItem("\(fileName) - 重试后校验失败: \(errorMessage)")
                        } else {
                            await progressTracker.addFailedItem("\(fileName) - 重试后校验失败: 文件内容不一致")
                        }
                    } else {
                        // 校验成功，更新进度
                        _ = await progressTracker.incrementAndGetProgress(bytesProcessed: fileSize)
                    }
                } catch {
                    // 重试复制失败
                    await progressTracker.addFailedItem("\(fileName) - 重试复制失败: \(error.localizedDescription)")
                }
                
                // 更新进度 - 使用当前索引+1作为处理文件数
                processedFiles = index + 1
                progress = Double(index + 1) / Double(filesToRetry.count)
                self.markProgressNeedsUpdate()
                
                // 更新状态消息，显示当前进度
                let speedText = currentSpeed > 0 ? " - \(formatSpeed(currentSpeed))" : ""
                statusMessage = "正在重试复制: \(processedFiles)/\(filesToRetry.count) 文件\(speedText)"
            }
        }
        
        // 停止进度更新定时器
        stopProgressUpdateTimer()
        
        // 强制执行一次垃圾回收
        autoreleasepool {
            // 这里不需要任何代码，只是促使自动释放池释放不再需要的内存
        }
        
        // 合并失败项目
        let newFailedItems = await progressTracker.getFailedItems()
        failedFiles.append(contentsOf: newFailedItems)
        
        // 完成当前目标，进入下一个
        if !cancelRequested {
            // 确保进度显示为100%
            await MainActor.run {
                self.progress = 1.0
            }
            
            // 处理下一个目标
            await processNextDestination()
        }
    }
    
    // 处理下一个目标的方法
    @MainActor
    private func processNextDestination() async {
        currentDestinationIndex += 1
        
        // 重置调整后的文件夹名称，为新的目标做准备
        adjustedDestinationFolderName = nil
        
        if currentDestinationIndex < activeDestinationPaths.count {
            // 还有更多目标，继续处理
            statusMessage = "已完成目标 \(currentDestinationIndex) 的校验，开始处理下一个目标"
            
            do {
                try await processCopyToNextDestination()
            } catch {
                statusMessage = "复制过程发生错误: \(error.localizedDescription)"
                isProcessing = false
                currentPhase = .idle
                
                // 结束后台活动
                endBackgroundActivity()
            }
        } else {
            // 所有目标都已处理完成
            statusMessage = "所有复制和校验操作已完成"
            isProcessing = false
            currentPhase = .idle
            
            // 结束后台活动
            endBackgroundActivity()
        }
    }
    
    // 优化的文件内容校验方法
    private func verifyFileContent(sourceURL: URL, destURL: URL) async -> (success: Bool, errorMessage: String?) {
        do {
            // 获取文件大小
            let fileAttributes = try fileManager.attributesOfItem(atPath: sourceURL.path)
            let fileSize = fileAttributes[.size] as? UInt64 ?? 0
            let isLargeFile = fileSize > smallFileThreshold
            
            // 创建两个文件句柄
            let sourceHandle = try FileHandle(forReadingFrom: sourceURL)
            let destHandle = try FileHandle(forReadingFrom: destURL)
            
            // 确保文件句柄在函数结束时关闭
            defer {
                try? sourceHandle.close()
                try? destHandle.close()
            }
            
            var hasher1 = SHA256()
            var hasher2 = SHA256()
            var isIdentical = true
            
            // 分块读取和比较
            var sourceData: Data?
            var destData: Data?
            var bytesProcessed: UInt64 = 0
            let progressUpdateThreshold: UInt64 = 10 * 1024 * 1024 // 10MB
            
            repeat {
                // 使用较小的缓冲区分块读取
                sourceData = try sourceHandle.read(upToCount: self.fileReadBufferSize)
                destData = try destHandle.read(upToCount: self.fileReadBufferSize)
                
                // 比较数据块
                if sourceData != destData {
                    isIdentical = false
                    break
                }
                
                // 更新哈希值（只在数据相同时继续计算）
                if let sourceData = sourceData, !sourceData.isEmpty {
                    hasher1.update(data: sourceData)
                    bytesProcessed += UInt64(sourceData.count)
                    
                    // 对于大文件，定期更新校验进度
                    if isLargeFile && bytesProcessed >= progressUpdateThreshold {
                        // 更新校验进度
                        let localBytesProcessed = bytesProcessed // 创建本地副本防止并发问题
                        await MainActor.run {
                            self.copiedBytes += localBytesProcessed
                            self.markProgressNeedsUpdate()
                        }
                        
                        // 重置已处理字节计数，只累计未报告的字节
                        bytesProcessed = 0
                        
                        // 允许其他任务执行
                        await Task.yield()
                    }
                }
                
                if let destData = destData, !destData.isEmpty {
                    hasher2.update(data: destData)
                }
                
            } while (sourceData != nil && !sourceData!.isEmpty) || (destData != nil && !destData!.isEmpty)
            
            // 如果内容长度不同，文件不一致
            if (sourceData == nil && destData != nil) || (sourceData != nil && destData == nil) {
                return (false, "文件长度不一致")
            }
            
            // 只有在所有数据块都匹配的情况下才比较最终哈希值
            if isIdentical {
                let sourceHash = hasher1.finalize()
                let destHash = hasher2.finalize()
                
                return (sourceHash == destHash, nil)
            } else {
                return (false, "文件内容不一致")
            }
        } catch {
            return (false, error.localizedDescription)
        }
    }
    
    // 分块计算文件MD5哈希值 - 更高效的实现
    private func calculateFileMD5Hash(fileURL: URL) throws -> String {
        let fileHandle = try FileHandle(forReadingFrom: fileURL)
        defer { try? fileHandle.close() }
        
        var hasher = Insecure.MD5()
        
        // 分块读取并更新哈希值
        while let data = try fileHandle.read(upToCount: fileReadBufferSize), !data.isEmpty {
            hasher.update(data: data)
        }
        
        let digest = hasher.finalize()
        return digest.map { String(format: "%02hhx", $0) }.joined()
    }
    
    // 开始后台活动，防止系统休眠和降低应用优先级
    private func startBackgroundActivity() {
        // 先结束可能已经存在的后台活动
        endBackgroundActivity()
        
        // 创建新的后台活动
        backgroundActivity = ProcessInfo.processInfo.beginActivity(
            options: [.userInitiated, .idleSystemSleepDisabled, .automaticTerminationDisabled, .suddenTerminationDisabled],
            reason: "文件复制操作"
        )
    }
    
    // 结束后台活动
    private func endBackgroundActivity() {
        if let activity = backgroundActivity {
            ProcessInfo.processInfo.endActivity(activity)
            backgroundActivity = nil
        }
    }
    
    // 确保在对象被释放时结束后台活动
    deinit {
        endBackgroundActivity()
    }
}

// 用于处理NSOpenPanel文件选择和右键菜单的代理类
class FileSelectionDelegate: NSObject, NSOpenSavePanelDelegate {
    static let shared = FileSelectionDelegate()
    
    func panel(_ sender: Any, shouldEnable url: URL) -> Bool {
        return true // 允许所有目录
    }
    
    // 为NSBrowser或NSOpenPanel添加右键菜单支持
    @objc func addContextMenu(_ sender: NSBrowser) {
        // 安装右键菜单
        if let browser = findBrowserInPanel() {
            let menu = NSMenu(title: "操作")
            let newFolderItem = NSMenuItem(title: "新建文件夹", action: #selector(createNewFolder(_:)), keyEquivalent: "n")
            newFolderItem.target = self
            menu.addItem(newFolderItem)
            
            let renameItem = NSMenuItem(title: "重命名", action: #selector(renameFolder(_:)), keyEquivalent: "r")
            renameItem.target = self
            menu.addItem(renameItem)
            
            browser.menu = menu
        }
    }
    
    // 查找OpenPanel中的浏览器组件
    private func findBrowserInPanel() -> NSBrowser? {
        // 尝试在活动窗口中查找浏览器控件
        if let panel = NSApplication.shared.mainWindow as? NSOpenPanel {
            if let browser = findBrowserInView(panel.contentView) {
                return browser
            }
        }
        return nil
    }
    
    // 递归查找NSBrowser
    private func findBrowserInView(_ view: NSView?) -> NSBrowser? {
        guard let view = view else { return nil }
        
        if let browser = view as? NSBrowser {
            return browser
        }
        
        for subview in view.subviews {
            if let browser = findBrowserInView(subview) {
                return browser
            }
        }
        
        return nil
    }
    
    // 创建新文件夹
    @objc func createNewFolder(_ sender: Any) {
        guard let panel = NSApplication.shared.mainWindow as? NSOpenPanel else { return }
        
        // 获取当前目录
        guard let currentURL = panel.directoryURL else { return }
        
        // 创建新文件夹名称
        let newFolderName = "新建文件夹"
        let fileManager = FileManager.default
        var newFolderURL = currentURL.appendingPathComponent(newFolderName)
        
        // 检查文件夹是否已存在，如果存在则添加数字后缀
        var counter = 1
        while fileManager.fileExists(atPath: newFolderURL.path) {
            newFolderURL = currentURL.appendingPathComponent("\(newFolderName) \(counter)")
            counter += 1
        }
        
        do {
            // 创建文件夹
            try fileManager.createDirectory(at: newFolderURL, withIntermediateDirectories: true, attributes: nil)
            
            // 刷新面板视图
            panel.directoryURL = panel.directoryURL
            
            // 选中并准备重命名 - 使用if let _ 来检查浏览器是否存在但不使用
            if findBrowserInPanel() != nil {
                // 模拟选中并开始编辑
                // 注意：由于SwiftUI限制，这里可能需要更复杂的实现
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    // 尝试通知面板刷新
                    panel.validateVisibleColumns()
                }
            }
        } catch {
            // 创建文件夹失败，显示错误
            let alert = NSAlert()
            alert.messageText = "创建文件夹失败"
            alert.informativeText = error.localizedDescription
            alert.alertStyle = .warning
            alert.addButton(withTitle: "确定")
            alert.runModal()
        }
    }
    
    // 重命名文件夹
    @objc func renameFolder(_ sender: Any) {
        guard let panel = NSApplication.shared.mainWindow as? NSOpenPanel else { return }
        
        // 获取选中的文件/文件夹
        guard let selectedURL = panel.urls.first else { return }
        
        // 创建重命名输入框
        let alert = NSAlert()
        alert.messageText = "重命名"
        alert.informativeText = "请输入新名称:"
        alert.alertStyle = .informational
        alert.addButton(withTitle: "确定")
        alert.addButton(withTitle: "取消")
        
        let input = NSTextField(frame: NSRect(x: 0, y: 0, width: 200, height: 24))
        input.stringValue = selectedURL.lastPathComponent
        alert.accessoryView = input
        
        if alert.runModal() == .alertFirstButtonReturn {
            let newName = input.stringValue
            if !newName.isEmpty {
                let newURL = selectedURL.deletingLastPathComponent().appendingPathComponent(newName)
                
                do {
                    try FileManager.default.moveItem(at: selectedURL, to: newURL)
                    // 刷新面板
                    panel.directoryURL = panel.directoryURL
                } catch {
                    // 重命名失败，显示错误
                    let errorAlert = NSAlert()
                    errorAlert.messageText = "重命名失败"
                    errorAlert.informativeText = error.localizedDescription
                    errorAlert.alertStyle = .warning
                    errorAlert.addButton(withTitle: "确定")
                    errorAlert.runModal()
                }
            }
        }
    }
} 
